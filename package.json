{"name": "@ali/tbpc-pop-dynamic-common", "version": "1.0.20", "description": "业务pop模板集合", "files": ["dist"], "main": "dist/index.umd.es5.production.js", "exports": {".": {"default": {"types": "./dist/index.umd.es5.production.js", "default": "./dist/index.umd.es5.production.js"}}, "./*": "./*"}, "sideEffects": ["dist/*", "*.scss", "*.less", "*.css"], "scripts": {"start": "ice-pkg start", "build": "ice-pkg build", "prepublishOnly": "npm run build", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "lint": "npm run eslint && npm run stylelint"}, "keywords": ["ice", "react", "component"], "dependencies": {"@ali/pc-pop-sdk": "^1.0.17", "@ali/pcom-tbpc-venue-utils": "^1.0.0", "@ice/jsx-runtime": "^0.2.0", "@swc/helpers": "^0.5.1", "react-draggable": "^4.4.6"}, "devDependencies": {"@ali/pkg-plugin-dev": "^1.0.0", "@ali/pkg-plugin-dev-client": "^1.0.0", "@applint/spec": "^1.2.3", "@ice/pkg": "^1.0.0", "@ice/pkg-plugin-jsx-plus": "^1.0.3", "@ice/runtime": "^1.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "stylelint": "^15.0.0"}, "peerDependencies": {"react": "^17 || ^18"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "license": "MIT"}