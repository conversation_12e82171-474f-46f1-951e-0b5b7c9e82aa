import * as React from 'react';
import PriceReductionReminder from './templates/PriceReductionReminder';
import SlideDrawer from './templates/SlideDrawer';
import SuspendedPendant from './templates/SuspendedPendant';
import MyBenefitReminder from './templates/MyBenefitReminder';
import SurpriseDoubleRedpacket from './templates/SurpriseDoubleRedpacket';
import SurpriseDoubleCoupon from './templates/SurpriseDoubleCoupon';
import { popData } from './mock/data';

export default function (_props: PopSingle) {
  const props = popData?.data?.value?.popList?.[1];
  const { templateCode = 'surpriseConsumerCoupon' } = props?.template || {};
  console.log('templateCode', templateCode)
  if (templateCode === 'slideDrawerStaticResPop') {
    return <SlideDrawer {...props} />;
  } else if (templateCode === 'slideStaticResPop') {
    return <SuspendedPendant {...props} />;
  } else if (templateCode === 'priceReductionAndKeyItemsPop') {
    return <PriceReductionReminder {...props} />;
  } else if (templateCode === 'myBenefitRemindPop') {
    return <MyBenefitReminder {...props} />;
  } else if (templateCode === 'surpriseDoubleRedpacket') {
    return <SurpriseDoubleRedpacket {...props} />;
  } else if (templateCode === 'surpriseConsumerCoupon') {
    return <SurpriseDoubleCoupon {...props} />;
  }

  return null;
}
