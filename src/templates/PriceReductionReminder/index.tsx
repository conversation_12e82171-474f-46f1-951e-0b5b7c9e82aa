import { showPop, closePop, reportEXP, reportCLK, reportCLOSE } from "@ali/pc-pop-sdk";
import { MouseEvent, useCallback, useEffect, useMemo, useState } from "react";
import styles from './index.module.less';
import { IPriceReductionItem, queryBackItems, queryBag } from "./request";
import ReduceItem from "./ReduceItem";
// import { gapMock, itemsMock } from '../../mock/data';
import BottomItem from "./BottomItem";
import { appendParamsToUrl, isTrue } from "@ali/pcom-tbpc-venue-utils";
import PopMask from '../../components/MaskContainer';

/**
 * 数组顺序随机打乱
 */
function shuffleArray(arr: any[]) {
  try {
    if (!Array.isArray(arr)) return [];
    // 创建一个数组的副本，避免修改原数组
    let array = [...arr];
    let currentIndex = array.length, randomIndex;

    // 当还有未打乱的元素时，继续循环
    while (currentIndex !== 0) {
      // 随机选择一个未打乱的元素
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex -= 1;

      // 交换当前元素和随机选中的元素
      [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
    }
    return array;
  } catch {
    return arr;
  }
}

export default (props: PopSingle) => {
  const { page, template, activity } = props || {};
  const {
    // 降价提醒时的降价xx的前缀文案
    hairTitle = '您购物车的商品最高已降',
    // 弹框背景色
    bgColor = '#FF7340',
    // 降价提醒时的标题图
    bgImageBefore = 'https://img.alicdn.com/imgextra/i2/O1CN01QmODTl1d5mXOOadk3_!!6000000003685-2-tps-864-80.png',
    // 推品时的标题图
    bgImageAfter = 'https://img.alicdn.com/imgextra/i1/O1CN010AzncD1k9dB7RtLnm_!!6000000004641-2-tps-864-80.png',
    // 阿拉丁资源位ID，推品时消费，支持单个/多个，多个时逗号分割
    resId, // = '36042940,36044924',
    // 降价提醒时的行动文案
    actionBefore = '去购物车查看',
    // 推品时的行动文案
    actionAfter = '去抢购',
    // 降价提醒时的跳转链接
    jumpUrl = 'https://cart.taobao.com',
    // 推品时的跳转链接
    jumpUrlAfter = 'https://www.taobao.com',
    // 1-开启、0-关闭，若开启，则跳过降价提醒直接推品，让该pop发挥更大用处
    disableReductionReminder = 0,
  } = page?.content || {};
  const { displayStyle = "window" } = template || {};
  const { sceneCode = "", activityId } = activity || {};
  const [priceReductionItems, setPriceReductionItems] = useState<Array<IPriceReductionItem>>([]);
  const [bottomItems, setBottomItems] = useState<Array<any>>([]);

  const [url, btnDesc, itemList, titleImg, isShowItems] = useMemo(() => {
    // 视觉要求降价提醒的时候最多4个
    const list1 = priceReductionItems?.slice(0, 4) || [];
    // 视觉要求推品的时候最多展示3个
    const list2 = shuffleArray(bottomItems)?.slice(0, 3) || [];
    // 所见即所得链接, 仅会场才拼接itemId, 购物车带着itemId会导致去了老车等异常问题
    const jumpUrlByItemIds = jumpUrl?.indexOf('/wow/a/act/') > -1 ? appendParamsToUrl(jumpUrl, {
      itemId: list1?.map((item: any) => item.itemId).join(','),
    }) : jumpUrl;
    const jumpUrlAfterByItemIds = jumpUrlAfter?.indexOf('/wow/a/act/') > -1 ? appendParamsToUrl(jumpUrlAfter, {
      itemId: list2?.map((item: any) => item.itemId).join(','),
    }): jumpUrlAfter;
    if (isTrue(disableReductionReminder)) {
      return [jumpUrlAfterByItemIds, actionAfter || actionBefore, list2, bgImageAfter, true];
    } else if (priceReductionItems?.length > 0) {
      return [jumpUrlByItemIds, actionBefore, list1, bgImageBefore, false];
    } else if (bottomItems?.length > 0) {
      return [jumpUrlAfterByItemIds, actionAfter, list2, bgImageAfter, true];
    }
    return [jumpUrlAfterByItemIds, actionAfter || actionBefore, list2, bgImageAfter, true];
  }, [jumpUrl, jumpUrlAfter, disableReductionReminder, priceReductionItems?.length, bottomItems?.length]);

  const handleShowPop = useCallback((count: number, isItemMode: boolean) => {
    showPop({
      displayStyle,
      sceneCode,
    });
    reportEXP({
      displayStyle,
      sceneCode,
      selfDefine: {
        itemCount: count,
        showMode: isItemMode ? 'bottomItemMode' : 'priceReductionMode',
      },
    });
  }, [displayStyle, sceneCode]);

  const handleClickPop = useCallback(() => {
    reportCLK({
      displayStyle,
      sceneCode,
      selfDefine: {
        itemCount: itemList?.length,
        showMode: isShowItems ? 'bottomItemMode' : 'priceReductionMode',
      },
    });
    closePop({
      displayStyle,
      sceneCode,
    });
  }, [displayStyle, sceneCode, url, itemList, isShowItems]);

  const handleClosePop = useCallback((e: MouseEvent<HTMLDivElement>) => {
    // 阻止冒泡和默认跳转事件
    e.stopPropagation();
    e.preventDefault();
    closePop({
      displayStyle,
      sceneCode,
    });
    reportCLOSE({
      displayStyle,
      sceneCode,
    });
  }, [displayStyle, sceneCode]);

  const fetchItems = useCallback(() => {
    if (resId) {
      queryBackItems({
        resId: resId?.toString().replace(/[，；;]/g, ','),
      }, props).then(data => {
        if (data?.length > 0) {
          setBottomItems(data);
          handleShowPop(data?.length, true);
        }
      });
    }
  }, [resId]);

  useEffect(() => {
    if (isTrue(disableReductionReminder)) { // 直接推品
      fetchItems();
    } else {
      queryBag(props).then(data => {
        if (data?.length > 0) {
          if (data.length === 1) { 
            const { gap, price } = data?.[0] || {};
            // 只有一个降价商品且降价幅度小于95折，则不提醒降价
            if (gap / price < 0.05) {
              fetchItems();
              return;
            }
          }
          setPriceReductionItems(data);
          handleShowPop(data?.length, false);
        } else {
          // 走兜底，推品
          fetchItems();
        }
      });
    }
    // setPriceReductionItems(gapMock.slice(0,4));
    // setBottomItems(itemsMock.slice(0,4));
  }, [disableReductionReminder]);

  if (!priceReductionItems?.length && !bottomItems?.length) {
    return null;
  }

  return (
    <PopMask>
      <div className={styles.wrap} style={{ backgroundColor: bgColor }}>
        <div className={styles.closeBtn} onClick={handleClosePop}>
          <div className={styles.cross} />
        </div>
        <div className={styles.header}>
          <div 
            className={styles.titleImage} 
            style={{ 
              backgroundImage: `url(${titleImg})`,
            }}
          />
          {
            priceReductionItems?.length > 0
            &&
            <div className={styles.biggestGap}>
              {hairTitle}
              <span className={styles.unit}>￥</span>
              <span className={styles.gap}>
                {
                  priceReductionItems.sort((a: any, b: any) => b.gap - a.gap)[0]?.gap
                }
              </span>
            </div>
          }
        </div>
        <div className={styles.contentWrap}>
          <a 
            className={styles.content}
            href={url}
            target="_blank"
            data-spm={`d_${activityId}`} 
            onClick={() => handleClickPop()}
          >
            {
              itemList.map((item, index) => {
                return (
                  isShowItems
                  ?
                  <BottomItem key={index} {...item} />
                  :
                  <ReduceItem key={index} {...item} />
                );
              })
            }
          </a>
        </div>
        <div className={styles.footerWrap}>
          <a 
            className={styles.footer}
            href={url}
            target="_blank"
            data-spm={`d_${activityId}`} 
            onClick={() => handleClickPop()}
          >
            {btnDesc}
          </a>
        </div>
      </div>
    </PopMask>
  );
}