.redPacketReminderWrapper {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  background-image: url('https://img.alicdn.com/imgextra/i2/O1CN01Q42tK01PkdFDCcuvL_!!6000000001879-2-tps-516-592.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.redPacketReminderTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  margin: 0 auto;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  white-space: nowrap;
  width: auto; 
}

.redPacketPopupWrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.redPacketPopupContent {
  width: 252px;
  height: 264px;
  border-radius: 16px;
  padding: 24px;
}

.redPacketPopupTitle {
  margin-bottom: 24px;
  margin-top: 4px;
}

.redPacketPopupTitleText {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  color: rgba(0, 0, 0, 0.92);
  height: 40px;
}

.redPacketPopupTitleAmount {
  color: #ff2626;
  font-size: 40px;
  font-weight: bold;
  margin: 0 4px;
  font-family: Inter V;
  transform: translateY(2px);
  display: inline-block;
}

.redPacketPopupTitleAmountUnit {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  color: #ff2626;
}

.redPacketPopupTitleSubtitle {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  color: rgba(0, 0, 0, 0.92);
}

.redPacketUi {
  border-radius: 8px;
  display: flex;
  margin-bottom: 36px;
  width: 252px;
  height: 80px;
}

.redPacketUiLeft {
  color: #f52525;
  font-size: 28px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -12px;
  width: 104px;
  height: 82px;
  flex-shrink: 0;

  span:first-child {
    font-size: 20px;
    margin-right: 2px;
    transform: translateY(3px);
  }
}

.redPacketUiRight {
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 82px;
}

.redPacketUiRightTitle {
  font-size: 16px;
  margin-bottom: 2px;
  font-weight: 500;
}

.redPacketUiRightExpireDate {
  font-size: 12px;
}

.redPacketPopupAction {
  text-align: center;
}

.redPacketPopupActionBtn {
  background: #ff2b2b;
  color: #fff;
  border-radius: 12px;
  font-size: 16px;
  cursor: pointer;
  width: 250px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}
.redPacketPopupActionBtn:hover {
  color: #fff;
}

.redPacketPopupClose {
  position: absolute;
  right: -122px;
  top: -66px;
  cursor: pointer;
  > img {
    width: 48px;
    height: 48px;
  }
}
