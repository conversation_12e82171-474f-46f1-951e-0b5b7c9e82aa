import { CSSProperties, memo, useCallback, useState, useEffect } from 'react';
import styles from './index.module.less';
import Countdown from '../CouponReminder/countDown';

import { queryRedEnvelopeUseUrl } from '../../services';

const CLOSE_ICON =
  'https://img.alicdn.com/imgextra/i1/O1CN01OmAbeT1uOsWUI9jMd_!!6000000006028-2-tps-96-96.png';
const DEFAULT_ACTION_URL =
  'https://i.taobao.com/my_itaobao/coupon?defaultTab=redEnvelope';

interface IProps {
  displayTotalFee: string;
  activityId: number;
  actionUrl?: string;
  title: string;
  gmtInvalid?: string;
  invalidTimestamp: number;
  subBizType: number;
  actionClickHandler?: () => void;
  closeHandler?: (e: React.MouseEvent<HTMLDivElement>) => void;
  showCloseIcon?: boolean;
  wrapperStyle?: CSSProperties;
  jumpUrl: string;
  actionAfter: string;
  props: PopSingle;
}

function RedPacketReminder(props: IProps) {
  const {
    title,
    displayTotalFee,
    gmtInvalid,
    invalidTimestamp,
    subBizType,
    activityId,
    actionClickHandler,
    closeHandler,
    showCloseIcon = true,
    wrapperStyle,
    jumpUrl = DEFAULT_ACTION_URL,
    actionAfter = "立即使用"
  } = props;

  const [finalJumpUrl, setFinalJumpUrl] = useState(jumpUrl);
  const [isUrlLoading, setIsUrlLoading] = useState(true);

  useEffect(() => {
    const fetchJumpUrl = async () => {
      setIsUrlLoading(true);
      try {
        const resJumpUrlRes = await queryRedEnvelopeUseUrl(props?.props);
        if (Object.keys(resJumpUrlRes?.data || {}).length > 0) {
          const matchedItem = Object.values(resJumpUrlRes.data).find(
            item => item?.ruleId?.toString() === subBizType?.toString()
          );
          if (matchedItem?.jumpUrl) {
            setFinalJumpUrl(matchedItem.jumpUrl);
          }
        }
      } catch (error) {
        console.error('Failed to fetch jump URL:', error);
      } finally {
        setIsUrlLoading(false);
      }
    };
    fetchJumpUrl();
  }, [subBizType, jumpUrl]);

  const clickHandler = useCallback(
    (e: React.MouseEvent<HTMLAnchorElement>) => {
      if (typeof actionClickHandler === 'function') {
        actionClickHandler();
      }
      const timer = setTimeout(() => {
        if (typeof closeHandler === 'function') {
          closeHandler();
        }
      }, 500);

      return () => clearTimeout(timer);
    },
    [closeHandler, actionClickHandler]
  );

  return (
    <div
      className={styles.redPacketReminderWrapper}
      style={wrapperStyle}
      role='dialog'
      aria-label='红包提醒'
    >
      <div className={styles.redPacketReminderTitle}>
        你的红包
        <Countdown
          backgroundColor='#fff'
          textColor='#ff0036'
          endTime={invalidTimestamp}
        />
        后失效
      </div>
      <div className={styles.redPacketPopupContent}>
        <div className={styles.redPacketPopupTitle}>
          <div className={styles.redPacketPopupTitleText}>
            你有
            <span className={styles.redPacketPopupTitleAmount}>
              {displayTotalFee}
            </span>
            <span className={styles.redPacketPopupTitleAmountUnit}>元</span>
          </div>
          <div className={styles.redPacketPopupTitleSubtitle}>红包即将过期</div>
        </div>
        <div className={styles.redPacketUi} role='presentation'>
          <div className={styles.redPacketUiLeft}>
            <span style={{ fontSize: displayTotalFee?.length >= 4 ? '18px' : '20px' }} aria-hidden='true'>¥</span>
            <span style={{ fontSize: displayTotalFee?.length >= 4 ? '24px' : '28px' }}>{displayTotalFee}</span>
          </div>
          <div className={styles.redPacketUiRight}>
            <div className={styles.redPacketUiRightTitle}>{title}</div>
            {gmtInvalid && (
              <div className={styles.redPacketUiRightExpireDate}>
                {gmtInvalid}
              </div>
            )}
          </div>
        </div>
        <div className={styles.redPacketPopupAction}>
          <a
            href={isUrlLoading ? undefined : finalJumpUrl}
            target='_blank'
            data-spm={`d_${activityId}`}
            className={styles.redPacketPopupActionBtn}
            onClick={clickHandler}
          >
            {actionAfter}
          </a>
        </div>
      </div>
      {showCloseIcon && (
        <div
          className={styles.redPacketPopupClose}
          onClick={closeHandler}
          aria-label='关闭'
        >
          <img src={CLOSE_ICON} alt='' width='24' height='24' />
        </div>
      )}
    </div>
  );
}

export default memo(RedPacketReminder);
