import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.module.less';
import Countdown from './countDown';
import {
  IBenefit,
  requestCouponGoodsList,
  requestZkCouponGoodsList,
} from '../../services';
import { appendParamsToUrl } from '@ali/pcom-tbpc-venue-utils';

export const CLOSE_ICON =
  'https://gw.alicdn.com/imgextra/i4/O1CN017nCniQ1orpn54vFMV_!!6000000005279-2-tps-96-96.png';

const defaultJumpUrl =
  'https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-546975';

interface IProps {
  activityId: number;
  benefitList: IBenefit[];
  actionClickHandler?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
  closeHandler?: (e?: React.MouseEvent<HTMLDivElement>) => void;
  jumpUrl: string;
  actionAfter: string;
  props: PopSingle;
}

interface goodsItem {
  itemTitle: string;
  itemId: string;
  itemPrice: string;
  itemImg: string;
  itemUrl: string;
  gatherPrice: {
    amount: string;
    amountYuan: string;
    amountInteger: string;
    amountSuffix: string;
  };
  gatherPriceTitle: string;
}

interface zkGoodsItem {
  newShowPrice: any;
  // 商品项接口定义
  brandName: string; // 品牌名称
  serviceTagList: []; // 服务标签列表
  whiteImg: string; // 白底图链接
  itemId: string; // 商品ID
  distinctId: string; // 区分ID
  trackParams: {
    // 跟踪参数
    trackInfo: string; // 跟踪信息
    clickTrackInfo: string; // 点击跟踪信息
  };
  activityPrice: string; // 活动价
  salesSite: string; // 销售站点
  showPrice: string; // 显示价格
  priceTxtEntity: {
    // 价格文本实体
    preText: string; // 前缀文本
    text: string; // 文本内容
    id: string; // ID
    type: string; // 类型
  };
  juId: string; // 聚划算ID
  attributes: {
    // 属性
    tkId: string; // 淘客ID
  };
  id: string; // 商品唯一标识
  yeWuTag: [string]; // 业务标签数组
  shortName: string; // 简短名称
  benefitAbovePriceEntity: {
    // 优惠价格实体
    text: string; // 文本内容
    id: string; // ID
    type: string; // 类型
  };
  categoryId: string; // 分类ID
  firstCat: string; // 第一级分类ID
  __pos__: string; // 位置标记
  __track__: string; // 跟踪标记
  bybtDetailUrl: string; // 详情页URL
}

function CouponReminderPop(props: IProps) {
  const {
    benefitList,
    actionClickHandler,
    closeHandler,
    activityId,
    jumpUrl = defaultJumpUrl,
    actionAfter = '立即使用',
  } = props || {};
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [activeBenefit, setActiveBenefit] = useState<
    (IBenefit & { goodsList: goodsItem[]; curPageUrl: string }) | null
  >(
    benefitList?.[0]
      ? { ...benefitList[0], goodsList: [], curPageUrl: '' }
      : null,
  );

  // 把多张券参数都传递到url上
  const couponList = useMemo(() => {
    return benefitList?.length > 1
      ? benefitList?.map((benefit) => {
          return {
            g_couponId: benefit.extraData?.templateCode,
            g_couponGroupId: benefit.extraData?.couponTag,
          };
        })
      : [];
  }, [benefitList]);

  const clickHandler = useCallback((e: React.MouseEvent<HTMLAnchorElement>) => {
    // 跳转之后关闭pop
    setTimeout(() => {
      if (typeof closeHandler === 'function') {
        closeHandler();
      }
    }, 500);
    if (typeof actionClickHandler === 'function') {
      actionClickHandler(e);
    }
  }, []);

  useEffect(() => {
    if (activeBenefit && !activeBenefit.goodsList?.length) {
      setIsLoading(true);
      setHasError(false);

      if (activeBenefit.subAssetType === 'zk') {
        const resourceId = activeBenefit?.resourceId;
        const curPageUrl = activeBenefit?.jumpUrl;
        const params = {
          curPageUrl,
          bizId: '443',
          pageSize: 8,
          pageIndex: 1,
          count: 50,
          resId: resourceId,
          appId: resourceId,
          extParam: '{}',
        };

        requestZkCouponGoodsList(params, props.props)
          .then((res) => {
            if (res?.data?.length) {
              const itemIds = res?.data
                .slice(0, 4)
                .map((item: { itemId: string }) => item.itemId)
                .join(',');

              const _activeBenefit = {
                ...activeBenefit,
                curPageUrl: appendParamsToUrl(params?.curPageUrl, {
                  itemId: itemIds,
                  np: itemIds,
                }),
                jumpUrl: appendParamsToUrl(activeBenefit?.jumpUrl || '', {
                  itemId: itemIds,
                  np: itemIds,
                }),
                goodsList: res.data,
              };
              setActiveBenefit(_activeBenefit);
            } else {
              setActiveBenefit({
                ...activeBenefit,
                curPageUrl: params.curPageUrl,
                goodsList: [],
              });
            }
          })
          .catch(() => {
            setHasError(true);
          })
          .finally(() => {
            setIsLoading(false);
          });
      } else {
        const couponId = activeBenefit.extraData?.templateCode;
        const couponGroupId = activeBenefit.extraData?.couponTag;
        const baseUrl =
          'https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr';
        const curPageUrl =
          couponList?.length >= 2
            ? `${baseUrl}?&wh_pid=daily-546975&g_couponId=${couponId}&g_couponGroupId=${couponGroupId}&coupon_list=${encodeURIComponent(
                JSON.stringify(couponList),
              )}`
            : `${baseUrl}?&wh_pid=daily-546975&g_couponId=${couponId}&g_couponGroupId=${couponGroupId}`;

        const params = {
          curPageUrl,
          appId: '19564324',
          bizId: '20210830',
          backupParams:
            'pageSize,pageIndex,count,bizId,appId,resId,curPageUrl,extParam',
          pageSize: 8,
          pageIndex: 1,
          count: 500,
          resId: '19564324',
          extParam: '{}',
        };

        requestCouponGoodsList(
          {
            curPageUrl,
            appId: params.appId,
            bizId: params.bizId,
            backupParams: params.backupParams,
            pageSize: params.pageSize,
            pageIndex: params.pageIndex,
            count: params.count,
            resId: params.resId,
            extParam: params.extParam,
          },
          props.props,
        )
          .then((res) => {
            if (res?.data?.length) {
              const _activeBenefit = {
                ...activeBenefit,
                curPageUrl: appendParamsToUrl(params.curPageUrl, {
                  itemId: res.data
                    .slice(0, 4)
                    .map((item: { itemId: string }) => item.itemId)
                    .join(','),
                }),
                goodsList: res.data,
              };
              setActiveBenefit(_activeBenefit);
            } else {
              setActiveBenefit({
                ...activeBenefit,
                curPageUrl: params.curPageUrl,
                goodsList: [],
              });
            }
          })
          .catch(() => {
            setHasError(true);
          })
          .finally(() => {
            setIsLoading(false);
          });
      }
    }
  }, [activeBenefit?.extraData?.templateCode, couponList]);

  const renderCoupon = (_benefit: IBenefit) => {
    if (_benefit.subAssetType === 'zk') {
      return (
        <>
          <div className={styles.couponLeft}>
            <div className={styles.couponValueContainer}>
              <p className={styles.couponRate}>
                {_benefit?.displayDiscountRate}
              </p>
              <p className={styles.couponType}>{_benefit?.title}</p>
            </div>
          </div>
          <div className={styles.couponConditionContainer}>
            <p className={styles.couponThreshold}>
              最高减{_benefit?.displayUpperLimitAmount}元
            </p>
            <p className={styles.couponExpiry}>
              有效期至 {_benefit?.gmtInvalid}
            </p>
          </div>
        </>
      );
    }

    return (
      <>
        <div className={styles.couponLeft}>
          <div className={styles.couponValueContainer}>
            <p className={styles.couponAmount}>{_benefit?.displayTotalFee}</p>
            <p className={styles.couponType}>{_benefit?.title}券</p>
          </div>
        </div>
        <div className={styles.couponConditionContainer}>
          <p className={styles.couponThreshold}>
            满{_benefit?.displayAmountThreshold}可用
          </p>
          <p className={styles.couponExpiry}>有效期至 {_benefit?.gmtInvalid}</p>
        </div>
      </>
    );
  };

  const renderGoodsList = (_benefit: IBenefit) => {
    if (isLoading) {
      return (
        <div className={styles.goodsList}>
          {Array(4)
            .fill(null)
            .map((_, index) => (
              <div
                key={`skeleton-${index}`}
                className={styles.goodsItemSkeleton}
              >
                <div className={`${styles.goodsImage} ${styles.skeleton}`} />
                <div
                  className={`${styles.price} ${styles.skeleton}`}
                  style={{ width: '60%', height: '20px' }}
                />
                <div
                  className={`${styles.afterDiscount} ${styles.skeleton}`}
                  style={{ width: '80%', height: '16px' }}
                />
              </div>
            ))}
        </div>
      );
    }

    if (!activeBenefit?.goodsList?.length || hasError) {
      return <div className={styles.emptyState} />;
    }

    if (_benefit.subAssetType === 'zk') {
      return (
        <div className={styles.goodsList}>
          {(
            (activeBenefit.goodsList.slice(0, 4) ||
              []) as unknown as zkGoodsItem[]
          ).map((item) => (
            <a
              key={item?.itemId}
              className={styles.goodsItem}
              target="_blank"
              href={item?.bybtDetailUrl}
              onClick={clickHandler}
            >
              <img src={item?.whiteImg} className={styles.goodsImage} />
              <div className={styles.price}>
                <span className={styles.currency}>{item?.newShowPrice?.pcDiscountPrice || item?.newShowPrice?.showPrice || item?.showPrice ? '¥' : ''}</span>
                <span className={styles.amount}>{item?.newShowPrice?.pcDiscountPrice || item?.newShowPrice?.showPrice || item?.showPrice || '??'}</span>
              </div>
              <div className={styles.afterDiscount}>
                <span className={styles.bold}>
                  {item?.benefitAbovePriceEntity?.text ||
                   item?.serviceTagList?.[0] ||
                    '火爆热卖中'
                  }
                </span>
              </div>
            </a>
          ))}
        </div>
      );
    }

    return (
      <div className={styles.goodsList}>
        {(activeBenefit.goodsList.slice(0, 4) || []).map((item) => (
          <a
            key={item?.itemId}
            className={styles.goodsItem}
            target="_blank"
            href={item?.itemUrl}
            onClick={clickHandler}
          >
            <img src={item?.itemImg} className={styles.goodsImage} />
            <div className={styles.price}>
              <span className={styles.currency}>¥</span>
              <span className={styles.amount}>{item?.itemPrice || '??'}</span>
            </div>
            {item?.gatherPrice?.amountYuan ? (
              <div className={styles.afterDiscount}>
                {item?.gatherPriceTitle}
                <span className={styles.bold}>¥</span>
                <span className={styles.priceBold}>
                  {item?.gatherPrice?.amountYuan}
                </span>
                {item?.gatherPrice?.amountSuffix}
              </div>
            ) : null}
          </a>
        ))}
      </div>
    );
  };

  const renderJumpUrl = () => {
    if (isLoading) {
      return undefined;
    }
    // 折扣券跳转链接，优先取下发的，卡券包兜底
    if (activeBenefit?.subAssetType === 'zk') {
      return activeBenefit?.jumpUrl || 'https://cart.taobao.com/cart.htm';
    }

    if (jumpUrl) {
      return appendParamsToUrl(jumpUrl, {
        g_couponId: activeBenefit?.extraData?.templateCode,
        g_couponGroupId: activeBenefit?.extraData?.couponTag,
      });
    }

    return activeBenefit?.curPageUrl;
  };

  return (
    <div className={styles.couponReminderWrapper}>
      <div className={styles.header}>
        你有{benefitList?.length}张优惠券即将失效
      </div>
      <div className={styles.top}>
        <Countdown
          remainingText="还剩"
          backgroundColor="#000"
          textColor="#fff"
          endTime={activeBenefit?.invalidTimestamp}
        />
      </div>
      <div className={styles.middle}>
        {/* 券展示 */}
        <div className={styles.couponWrapper}>
          {benefitList?.map((benefit) => {
            return (
              <div
                className={`${styles.couponBlock} ${
                  activeBenefit?.extraData?.templateCode ===
                    benefit?.extraData?.templateCode && benefitList?.length > 1
                    ? styles.activeCouponBlock
                    : ''
                }`}
                key={benefit?.extraData?.templateCode}
                style={{
                  cursor: benefitList?.length > 1 ? 'pointer' : 'default',
                  border:
                    benefitList?.length === 1
                      ? '2px solid #FF3333'
                      : '2px solid transparent',
                }}
                onClick={() => {
                  benefitList?.length > 1 && setActiveBenefit(benefit);
                }}
              >
                {renderCoupon(benefit)}
              </div>
            );
          })}
        </div>
        {/* 商品展示 */}
        <div className={styles.goodsWrapper}>
          {renderGoodsList(activeBenefit)}
        </div>
      </div>
      <div className={styles.bottom}>
        <a
          href={renderJumpUrl()}
          data-spm={`d_${activityId}`}
          target="_blank"
          className={`${styles.btn} ${
            isLoading || hasError ? styles.disabled : ''
          }`}
          onClick={(e) => {
            if (isLoading) {
              e.preventDefault();
              return;
            }
            clickHandler(e);
          }}
        >
          {actionAfter}
        </a>
      </div>
      <div className={styles.closeIconWrapper} onClick={closeHandler}>
        <img className={styles.closeIcon} src={CLOSE_ICON} />
      </div>
    </div>
  );
}

export default memo(CouponReminderPop);
