.couponReminderWrapper {
  width: 648px;
  // min-height: 466px;
  height: 535px;
  border-radius: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: default;
  background-color: #ffffff;
  padding: 24px;
  box-sizing: content-box;

  .header {
    height: 40px;
    font-size: 32px;
    font-weight: 600;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.92);
    white-space: nowrap;
    text-align: center;
    width: 100%;
    line-height: 40px;
    margin-top: 8px;
  }

  .top {
    width: 100%;
    height: 28px;
    line-height: 28px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    margin-top: 12px;
    margin-bottom: 24px;
  }

  .middle {
    .hairTitle {
      width: 80%;
      height: 24px;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      letter-spacing: 0;
      color: #ff330a;
      position: absolute;
      top: 38px;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;
      text-align: center;
    }

    .priceWrapper {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: baseline;
      margin: 60px auto 4px;
      color: #ff330a;
      font-family: AlibabaSans102v1TaoBao-Bold;
    }

    .priceUnit {
      height: 46px;
      font-size: 40px;
      font-weight: bold;
      line-height: 46px;
      letter-spacing: 0;
      margin-right: 2px;
    }

    .priceValue {
      height: 56px;
      font-size: 56px;
      font-weight: bold;
      line-height: 56px;
      letter-spacing: 0;
    }

    .subTitle {
      height: 24px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
      letter-spacing: 0;
      color: #ff330a;
    }

    .couponWrapper {
      display: flex;
      gap: 16px;

      .couponBlock {
        display: flex;
        background: #ffe5e5;
        border-radius: 8px;
        color: #ff3333;
        flex: 1;
        height: 80px;
        box-sizing: border-box;
        border: 2px solid transparent;

        .couponLeft {
          flex: 0 0 auto;
          padding-left: 18px;
          height: 80px;
          display: flex;

          .couponValueContainer {
            text-align: center;

            .couponAmount {
              font-size: 28px;
              font-weight: bold;
              margin: 0;
              line-height: 28px;
              font-family: Inter V;
              margin-top: 14px;

              &::before {
                content: '¥';
                font-size: 16px;
                font-family: Inter V;
              }
            }

            .couponRate {
              font-size: 28px;
              font-weight: bold;
              margin: 0;
              line-height: 28px;
              font-family: Inter V;
              margin-top: 14px;

              &::after {
                content: '折';
                font-size: 16px;
                font-family: Inter V;
              }
            }

            .couponType {
              font-size: 12px;
              margin-top: -2px;
              font-weight: 500;
            }
          }
        }

        .couponConditionContainer {
          flex: 1;
          padding-left: 32px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          height: 80px;

          .couponThreshold {
            font-size: 16px;
            margin: 0;
            line-height: 1.4;
            font-weight: 600;
          }

          .couponExpiry {
            font-size: 12px;
            margin: 4px 0 0;
            opacity: 0.8;
          }
        }
      }

      .activeCouponBlock {
        box-sizing: border-box;
        background-color: unset;
        height: 88px;
        cursor: pointer;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-image: url(https://img.alicdn.com/imgextra/i4/O1CN01sq8mCo1xn6Jds6eqD_!!6000000006487-2-tps-636-175.png);
      }
    }

    .goodsWrapper {
      margin-top: 14px;

      .goodsList {
        display: flex;
        gap: 16px;
        overflow-x: hidden;
        background-color: #ffe5e5;
        padding: 16px;
        border-radius: 12px;

        .goodsItem,
        .goodsItemSkeleton {
          flex: 1;
          text-align: center;
          background-color: #fff;
          border-radius: 8px;
          padding-bottom: 12px;

          .goodsImage {
            width: 142px;
            height: 142px;
            object-fit: cover;
            border-radius: 8px;
          }

          .price {
            margin-top: 2px;
            color: #ff2b2b;

            .currency {
              font-size: 14px;
              font-family: Inter V;
              font-weight: 600;
            }

            .amount {
              font-size: 16px;
              font-weight: 600;
              font-family: Inter V;
            }
          }

          .afterDiscount {
            margin-top: 2px;
            padding: 4px 8px;
            background: #fff1f1;
            border-radius: 8px;
            font-size: 12px;
            color: #ff2b2b;
            display: inline-block;
            line-height: 16px;
          }
        }
        .goodsItemSkeleton {
          flex-direction: column;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .bottom {
    width: 100%;
    margin-top: 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .btn {
      width: 200px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background: #ff2626;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0;
      color: #fff;
      position: relative;
      text-decoration: none;
      cursor: pointer;

      &:hover {
        text-decoration: none;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.24);
          border-radius: 12px;
        }
      }

      &:active {
        text-decoration: none;
      }
    }
  }

  .closeIconWrapper {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 40px;
    height: 40px;
    cursor: pointer;

    .closeIcon {
      width: 40px;
      height: 40px;
      object-fit: contain;
    }
  }
}

.countdownContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  white-space: nowrap; /* 防止换行 */
  .remainingText {
    color: rgba(0, 0, 0, 0.92);
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    margin-right: 10px;
  }
  .timeBlock {
    color: white;
    font-size: 16px;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.92);
    border-radius: 4px;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
  }

  .separator {
    color: rgba(0, 0, 0, 0.92);
    font-size: 24px;
    font-weight: bold;
    display: flex;
    align-items: center;
    height: 28px;
    margin: 0 8px;
    transform: translateY(-2px);
  }
}

.skeleton {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.bold {
  font-weight: 600;
  margin-left: 2px;
}

.priceBold {
  font-size: 16px;
  font-weight: 600;
  margin-right: 2px;
  font-family: Inter V;
}

.emptyState {
  width: 648px;
  height: 172px;
  background: url(https://img.alicdn.com/imgextra/i4/O1CN01A6Elds1EdnZ6G2ili_!!6000000000375-2-tps-1296-344.png) no-repeat center center;
  background-size: 100% 100%;
  margin-top: 60px;
  margin-bottom: 56px;
}
