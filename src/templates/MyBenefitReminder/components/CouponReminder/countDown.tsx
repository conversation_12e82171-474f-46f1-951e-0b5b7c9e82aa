import { useEffect, useRef, useState } from 'react';
import { queryCurrentTimestamp } from '@ali/pcom-tbpc-venue-utils';
import styles from './index.module.less';

interface CountdownProps {
  backgroundColor?: string;
  textColor?: string;
  remainingText?: string;
  endTime: number;
}

const Countdown: React.FC<CountdownProps> = ({
  backgroundColor = '',
  textColor = '',
  remainingText,
  endTime,
}) => {
  const [time, setTime] = useState({
    days: 0,
    hours: '00',
    minutes: '00',
    seconds: '00',
    showDays: false,
    initialized: false,
  });
  const startTimestamp = useRef<number>(new Date().getTime());
  const timerRef = useRef<NodeJS.Timeout>();

  // 使用固定宽度的时间块，防止数字变化导致宽度变化
  const timeBlockStyle = {
    backgroundColor: backgroundColor,
    color: textColor,
    minWidth: '28px',
    textAlign: 'center' as const,
    borderRadius: '4px',
  };

  // 大于24小时时的样式
  const dayTimeBlockStyle = {
    backgroundColor: backgroundColor || '#fff',
    color: textColor || '#ff0036',
    minWidth: '28px',
    textAlign: 'center' as const,
    borderRadius: '4px',
  };

  // 单位文本样式
  const unitStyle = {
    color: backgroundColor || '#fff',
    fontSize: '16px',
    fontWeight: '500' as const,
    margin: '0 4px',
  };

  const updateTime = () => {
    const now = startTimestamp.current + (Date.now() - startTimestamp.current); // Update local time based on server time
    const timeLeft = Math.max(0, endTime - now);

    // 计算天数、小时、分钟和秒
    const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    // 当剩余时间大于等于1天时，显示天数
    const showDays = days > 0;

    setTime({
      days,
      hours: String(hours).padStart(2, '0'),
      minutes: String(minutes).padStart(2, '0'),
      seconds: String(seconds).padStart(2, '0'),
      showDays,
      initialized: true,
    });
  };

  const fetchCurrentTimestamp = async () => {
    try {
      const { t = '' } = await queryCurrentTimestamp();
      if (t) {
        startTimestamp.current = parseInt(t, 10);
      } else {
        startTimestamp.current = Date.now();
      }
      updateTime();
    } catch (err) {
      console.error('Failed to fetch server timestamp:', err);
      startTimestamp.current = Date.now();
      updateTime();
    }
  };

  useEffect(() => {
    fetchCurrentTimestamp();
    timerRef.current = setInterval(updateTime, 1000);

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchCurrentTimestamp();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [endTime]);

  return (
    <div className={styles.countdownContainer}>
      {remainingText && (
        <span className={styles.remainingText}>{remainingText}</span>
      )}
      {!time.initialized ? null : time?.showDays ? (
        // 当剩余时间大于等于1天时，显示 xx天xx小时xx分 格式
        <>
          <span
            className={styles.timeBlock}
            style={dayTimeBlockStyle}
          >
            {time.days.toString().padStart(2, '0')}
          </span>
          <span style={unitStyle}>
            天
          </span>
          <span
            className={styles.timeBlock}
            style={dayTimeBlockStyle}
          >
            {time.hours}
          </span>
          <span style={unitStyle}>
            小时
          </span>
          <span
            className={styles.timeBlock}
            style={dayTimeBlockStyle}
          >
            {time.minutes}
          </span>
          <span style={{ ...unitStyle, marginRight: 0 }}>
            分
          </span>
        </>
      ) : (
        // 当剩余时间小于1天时，显示 xx:xx:xx 格式
        <>
          <span
            className={styles.timeBlock}
            style={timeBlockStyle}
          >
            {time.hours}
          </span>
          <span className={styles.separator} style={{ color: backgroundColor }}>
            :
          </span>
          <span
            className={styles.timeBlock}
            style={timeBlockStyle}
          >
            {time.minutes}
          </span>
          <span className={styles.separator} style={{ color: backgroundColor }}>
            :
          </span>
          <span
            className={styles.timeBlock}
            style={timeBlockStyle}
          >
            {time.seconds}
          </span>
        </>
      )}
    </div>
  );
};

export default Countdown;
