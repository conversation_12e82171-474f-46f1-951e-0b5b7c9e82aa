import {
  fetchDataWidthAld,
  IFetchAldDataParams,
  mtopRequest,
  isTrue,
} from '@ali/pcom-tbpc-venue-utils';
import { formatArrToObj } from '../utils';
import { reportRequestLogOnFail } from '@ali/pc-pop-sdk';

interface IRequestCouponListParams {
  // 项目名-统一命名为PcTaobao【必填】
  projectName: string;
  // 不同的视图唯一标识【必填】
  responseCode: string;
  // 自定义参数
  params: {
    bizType: string;
    bizId: number;
  };
}

export interface IBenefit {
  fundId?: string;
  amountThreshold: number;
  // 资产类型：coupon-优惠券
  assetType: string;
  subAssetType: string;
  currentTimestamp: number;
  // 门槛
  displayAmountThreshold: string;
  // 面额
  displayTotalFee: string;
  // 跳转凑单神器所需参数
  extraData: {
    subBizType?: number;
    couponTag: string;
    templateCode: number;
  };
  gmtInvalid: string;
  // 失效时间戳
  invalidTimestamp: number;
  title: string;
  totalFee: number;
  // 折扣券
  displayDiscountRate?: string;
  displayUpperLimitAmount?: string;
  resourceId?: string;
  jumpUrl?: string;
}

interface IValue {
  benefits: IBenefit[];
}

interface IRequestCouponListRes {
  code: number;
  value: IValue;
  message: string;
  errorCode: string;
}

// 获取催用优惠数据
export const requestReminderData = async ({
  projectName,
  responseCode,
  params,
}: IRequestCouponListParams, props: PopSingle): Promise<
  IRequestCouponListRes & { message: string; success: boolean }
> => {
  const api = 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView';
  const req = {
    projectName,
    responseCode,
    params,
  };
  try {
    const res = await mtopRequest({
      api,
      data: req,
    });
    if (res?.errorCode === 'SUCCESS' && res?.value?.benefits?.length > 0) {
      return {
        ...res,
        success: true,
      };
    } else {
      reportRequestLogOnFail({
        props,
        args: {
          api,
          req,
          res,
          traceId: res?.traceId || res?.data?.traceId,
          msg: res?.message || res.msg,
          code: res.code || res.errorCode,
        },
      });
    }
    return {
      data: {},
      message: '',
      success: false,
    } as any;
  } catch (err) {
    console.error(
      'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView:',
      err
    );
    reportRequestLogOnFail({
      props,
      args: {
        api,
        req,
        res: err,
        traceId: err?.traceId || err?.data?.traceId,
        msg: err?.message || err.msg,
        code: err.code || err.errorCode,
      },
    });
    return {
      code: 500,
      errorCode: 'SYSTEM_ERROR',
      data: err,
      message: err?.message || '系统错误',
      success: false,
    } as any;
  }
};

// 获取优惠券下商品数据
export function requestCouponGoodsList(params: IFetchAldDataParams, props: PopSingle) {
  return fetchDataWidthAld(params).then((res: any) => {
    const data = res.data[0]?.feedsList || res.data;
    if (res?.success && data?.length > 0) {
      return {
        data,
        hasMore: data?.length > 0,
        traceId: res.traceId,
      };
    } else {
      reportRequestLogOnFail({
        props,
        args: {
          api: '阿拉丁通用服务，查询优惠券下的商品',
          req: params,
          res,
          traceId: res?.traceId || res?.data?.traceId,
          msg: res?.message || res.msg,
          code: res.code || res.errorCode,
        },
      });
      return {
        data: [],
        hasMore: false,
        traceId: res?.traceId,
      };
    }
  });
}

// 获取折扣券优惠券下商品数据
export function requestZkCouponGoodsList(params: IFetchAldDataParams, props: PopSingle) {
  return fetchDataWidthAld(params).then((res: any) => {
    const data = res.data || [];
    if (res?.success && data?.length > 0) {
      return {
        data,
        hasMore: data?.length > 0,
        traceId: res.traceId,
      };
    } else {
      reportRequestLogOnFail({
        props,
        args: {
          api: '阿拉丁通用服务，查询优惠券下的商品',
          req: params,
          res,
          traceId: res?.traceId || res?.data?.traceId,
          msg: res?.message || res.msg,
          code: res.code || res.errorCode,
        },
      });
      return {
        data: [],
        hasMore: false,
        traceId: res?.traceId,
      };
    }
  });
}

/**
 * 查询红包的去使用链接
 * 取数消费示意：
 *  data[benefitItem?.[红包的唯一标识]]?.jumpUrl;
 *  其中benefitItem就是官亭下发的红包数据对象
 * 如果任意红包的跳转链接无法匹配到（此时表示运营没做人工干预），请使用卡券包链接作为兜底：https://i.taobao.com/my_itaobao/coupon?defaultTab=redEnvelope
 */
export const queryRedEnvelopeUseUrl = async (props: PopSingle): Promise<{
  data: {
    [x: string]: {
      ruleId: string;
      id: string;
      jumpUrl: string;
    }
  };
  success: boolean;
}> => {
  const RED_ENVELOPE_RES_ID = 35633211;
  const req = {
    params: JSON.stringify({
      resId: String(RED_ENVELOPE_RES_ID),
      bizId: "443"
    }),
  };
  try {
    const res = await mtopRequest({
      api: "mtop.tmall.kangaroo.core.service.route.AldLampServiceFixedResV2",
      data: req,
      needLogin: false,
    });

    const resultValue = res?.resultValue?.[RED_ENVELOPE_RES_ID] || {};
    if (isTrue(resultValue?.success)) {
      const config = formatArrToObj(resultValue?.data, 'ruleId');
      return {
        data: config || {},
        success: Boolean(config)
      };
    }
    reportRequestLogOnFail({
      props,
      args: {
        api: '阿拉丁通用服务，查询红包的去使用链接',
        req,
        res,
        traceId: res?.traceId || res?.data?.traceId,
        msg: res?.message || res.msg,
        code: res.code || res.errorCode,
      },
    });
    
    return {
      data: {},
      success: false
    };
  } catch (err) {
    reportRequestLogOnFail({
      props,
      args: {
        api: '阿拉丁通用服务，查询红包的去使用链接',
        req,
        res: err,
        traceId: err?.traceId || err?.data?.traceId,
        msg: err?.message || err.msg,
        code: err.code || err.errorCode,
      },
    });
    return {
      data: {},
      success: false
    };
  }
};