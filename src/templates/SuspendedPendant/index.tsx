import React, { useEffect, useMemo, useState } from "react";
import Draggable from "react-draggable";
import { showPop, reportEXP, reportCLK } from "@ali/pc-pop-sdk";

// 悬浮挂件
const selfDefineSpm = "d_slideStaticResPop";
export default function SuspendedPendant(props: any) {
  const { page, template, activity } = props || {};
  const { bgImageBefore, displayPosition, jumpUrl } = page?.content || {};
  const { displayStyle = "float", attributes } = template || {};
  const { sceneCode = "", activityId = '' } = activity || {};
  const { distanceToBottom, zIndex } = attributes || {};
  // 定义状态来控制DOM的显示与隐藏当天点击隐藏过
  const [showDOM, setShowDOM] = useState(true);
  // 悬浮挂件这种类型只存储一个key不同的活动id场景code 不同
  const closeTimeKey = "tbpcSlideStaticResPopCloseTime";
  const sceneAndactivityUniqueKey = `${sceneCode}_${activityId}_close`;
  // 上下移动的时候禁止点击跳转
  const [startY, setStartY] = useState(0);
  const [startTime, setStartTime] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  const calculateBounds = useMemo(() => {
    return () => {
      const bottomPercentage = distanceToBottom || 0;
      const bottomPixels = (window.innerHeight * bottomPercentage) / 100;
      return {
        top: -(window.innerHeight - bottomPixels - 92),
        bottom: bottomPixels,
      };
    };
  }, [distanceToBottom]);

  useEffect(() => {
    // 从localStorage中获取点击叉叉的时间戳
    let clickTimeStamp;
    try {
      clickTimeStamp = JSON.parse(localStorage.getItem(closeTimeKey))?.[
        sceneAndactivityUniqueKey
      ];
    } catch (error) {}
    if (clickTimeStamp) {
      const todayStart = new Date(new Date().setHours(0, 0, 0, 0));
      const clickTime = new Date(parseInt(clickTimeStamp, 10));
      // 判断点击时间是否在当天内
      if (clickTime >= todayStart) {
        setShowDOM(false);
      } else {
        showPop({
          displayStyle,
          sceneCode,
        });
        reportEXP({
          displayStyle,
          sceneCode,
          selfDefine: {
            from: selfDefineSpm,
          },
        });
      }
    } else {
      showPop({
        displayStyle,
        sceneCode,
      });
      reportEXP({
        displayStyle,
        sceneCode,
        selfDefine: {
          from: selfDefineSpm,
        },
      });
    }
  }, [sceneCode, displayStyle]);

  const handleJump = () => {
    reportCLK({
      displayStyle,
      sceneCode,
      selfDefine: {
        from: selfDefineSpm,
      },
    });
  };

  const handleClose = (e: any) => {
    e?.preventDefault();
    // 点击叉叉时，将当前时间的时间戳存储到localStorage中
    const now = new Date().getTime();
    localStorage.setItem(
      closeTimeKey,
      JSON.stringify({
        [sceneAndactivityUniqueKey]: now?.toString(),
      }),
    );
    setShowDOM(false);
  };

  // 处理鼠标释放事件
  const handleMouseUp = () => {
    // 移除鼠标移动和鼠标释放事件监听器
    document.removeEventListener("mouseup", handleMouseUp);
  };

  const handleStart = (e: { clientY: React.SetStateAction<number>; }) => {
    setStartY(e.clientY);
    setStartTime(Date.now());
  };

  const handleStop = (e: { clientY: number; }) => {
    const endTime = Date.now();
    const distanceY = Math.abs(e.clientY - startY);
    // 判断是否为点击操作
    if (distanceY < 3 && endTime - startTime < 300) {
      setIsDragging(false);
    } else {
      setIsDragging(true);
    }
  };

  if (!showDOM || !bgImageBefore) {
    return null;
  }

  return (
    // @ts-ignore
    <Draggable
      axis="y"
      onStart={handleStart}
      onStop={handleStop}
      bounds={calculateBounds()}
      onMouseDown={(e) => {
        // 阻止默认的鼠标按下行为
        e.preventDefault();
        // 给 document 添加鼠标移动和鼠标释放事件监听器
        document.addEventListener("mouseup", handleMouseUp);
      }}
    >
      <div
        style={{
          width: '160px',
          height: '92px',
          position: 'fixed',
          cursor: 'pointer',
          zIndex: `${window.isNaN(+zIndex) ? -1 : +zIndex}`,
          ...(displayPosition === "left"
            ? {
                left: "16px",
                bottom: `${distanceToBottom}%`,
              }
            : {
                right: "16px",
                bottom: `${distanceToBottom}%`,
              }),
        }}
      >
        <div
          style={{
            width: '24px',
            height: '24px',
            backgroundImage: 'url("https://img.alicdn.com/imgextra/i2/O1CN01a0fi891XXMghrk6jE_!!6000000002933-2-tps-96-96.png")',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'contain',
            cursor: 'pointer',
            zIndex: 2,
            display: 'block',
            position: 'absolute',
            right: 0,
          }}
          onClick={handleClose}
        />
        <a
          data-spm={`d_${activityId}`}
          href={!isDragging && jumpUrl}
          target="_blank"
          style={{
            width: '160px',
            height: '80px',
            display: 'block',
            marginTop: '12px',
            ...displayPosition === "left" ? {} : { marginTop: "18px" }
          }}
        >
          <img
            src={bgImageBefore}
            style={{
              width: '160px',
              height: '80px',
              objectFit: 'contain',
            }}
            onClick={handleJump}
          />
        </a>
      </div>
    </Draggable>
  );
}
