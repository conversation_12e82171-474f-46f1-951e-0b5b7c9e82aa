import { memo } from "react";
import styles from "./index.module.less";


interface IRedpacketItemProps {
  bgImage?: string;
  hairTitle?: string;
  totalDisplayAmount?: string;
  desc?: string;
  action?: string;
  jumpUrl?: string;
  spmD?: string;
  actionClickHandler?: () => void;
  closeHandler?: () => void;
  wrapperStyle?: React.CSSProperties;
  hairStyle?: React.CSSProperties;
  showCloseIcon?: boolean;
  benefits?: any[];
}

const CLOSE_ICON =
  'https://img.alicdn.com/imgextra/i1/O1CN01OmAbeT1uOsWUI9jMd_!!6000000006028-2-tps-96-96.png';

function CouponItem(props: IRedpacketItemProps) {
  const { benefits = [], totalDisplayAmount = "" } = props || {};
  const {
    bgImage = 'https://img.alicdn.com/imgextra/i3/O1CN01tnKRT01yZeJLP2TE2_!!6000000006593-2-tps-640-764.png',
    hairTitle = "消费券",
    desc = "可叠加官方立减",
    action = "去使用",
    jumpUrl = "",
    spmD = "",
    actionClickHandler,
    closeHandler,
    showCloseIcon = true,
  } = props || {};


  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${month}.${day} ${hours}:${minutes}`;
  };

  const getValidTimeText = () => {
    const startTime = benefits[0]?.effectiveStartTimestamp;
    const endTime = benefits[0]?.effectiveEndTimestamp;
    if (!startTime || !endTime) return '';

    return `${formatDate(startTime)}-${formatDate(endTime)}可用`;
  };

  return (
    <div
      className={styles.redpacketItem}
      style={{ backgroundImage: `url(${bgImage})` }}
    >
      <div className={styles.redpacketItemTop}>
        <img
          className={styles.redpacketItemTopImage}
          src="https://img.alicdn.com/imgextra/i3/6000000007609/O1CN01N5K3pp264yiIiioaA_!!6000000007609-2-gg_dtc.png"
        />
        <span className={styles.redpacketItemTopText}>{hairTitle}</span>
      </div>

      <div className={styles.redpacketItemTitle}>
        <div className={styles.redpacketItemTitleRow}>
          <span className={styles.redpacketItemTitleText}>
            <span className={styles.redpacketItemTitleNormal}>恭喜获得</span>
            <span className={styles.redpacketItemTitleRed}>{totalDisplayAmount}元消费券</span>
          </span>
        </div>
        <div className={styles.redpacketItemSubtitle}>
          <span className={styles.redpacketItemSubtitleText}>{desc}</span>
        </div>
      </div>

      <div className={styles.redpacketItemCoupons}>
        {benefits?.slice(0,2)?.map((benefit, index) => (
          <div key={index} className={styles.redpacketItemCoupon}>
            <div className={styles.redpacketItemCouponTop}>
              <div className={styles.redpacketItemCouponAmount}>
                <span className={styles.redpacketItemCouponSymbol}>¥</span>
                <span className={styles.redpacketItemCouponValue}>{benefit.displayAmount}</span>
              </div>
            </div>
            <div className={styles.redpacketItemCouponBottom}>
              <span className={styles.redpacketItemCouponCondition}>满{benefit.displayStartFee}可用</span>
            </div>
          </div>
        ))}
      </div>
      <span className={styles.redpacketItemPeriod}>
        {getValidTimeText()}
      </span>
      <a
        className={styles.redpacketItemButton}
        href={jumpUrl}
        target="_blank"
        data-spm={spmD}
        onClick={() => {
          if (actionClickHandler) {
            actionClickHandler();
          }
          if (closeHandler) {
            closeHandler();
          }
        }}
      >
        <div className={styles.redpacketItemButtonInner}>
          <span className={styles.redpacketItemButtonText}>{action}</span>
        </div>
      </a>
      {showCloseIcon && (
        <div
          className={styles.redpacketItemClose}
          onClick={closeHandler}
          aria-label='关闭'
        >
          <img src={CLOSE_ICON} alt='' width='24' height='24' />
        </div>
      )}
    </div>
  );
}

export default memo(CouponItem);
