.redpacketItem {
  border-radius: 16px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  overflow: visible;
  width: 320px;
  height: 382px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  position: relative;
}

.redpacketItemTop {
  position: relative;
  
  &Image {
    width: 112px;
    height: 32px;
  }

  &Text {
    position: absolute;
    top: 14px;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    text-align: center;
  }
}

.redpacketItemTitle {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8px;

  &Row {
    display: flex;
    flex-direction: row;
    overflow: hidden;
  }

  &Text {
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
    text-align: center;
  }

  &Normal {
    color: rgba(0, 0, 0, 0.92);
  }

  &Red {
    color: #F52525;
  }
}

.redpacketItemSubtitle {
  margin-top: 4px;

  &Text {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #F52525;
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 197px;
    text-align: center;
  }
}

.redpacketItemCoupons {
  display: flex;
  flex-direction: row;
  width: 272px;
  height: 144px;
  margin-top: 6px;
  gap: 16px;
  justify-content: center;
}

.redpacketItemCoupon {
  border-radius: 13px;
  background-size: contain;
  background-image: url(https://img.alicdn.com/imgextra/i2/O1CN01XBuVtX1sdsHAJzNvw_!!6000000005790-2-tps-256-288.png);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  width: 128px;
  height: 144px;

  &Top {
    position: relative;
    height: 76px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &Bg {
    position: absolute;
    width: 192px;
    height: 76px;
    top: 0;
    left: -32px;
  }

  &Amount {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: flex-end;
  }

  &Symbol {
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    transform: translateY(3px);
  }

  &Value {
    font-size: 36px;
    font-weight: 600;
    color: #fff;
    line-height: 1;
  }

  &Bottom {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &Condition {
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
  }
}

.redpacketItemPeriod {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.92);
  margin-top: 12px;
}

.redpacketItemButton {
  border-radius: 12px;
  background: #F52525;
  width: 272px;
  height: 48px;
  margin-top: 12px;
  cursor: pointer;

  &Inner {
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &Text {
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
  }
}

.redpacketItemClose {
  position: absolute;
  right: -96px;
  top: 0px;
  cursor: pointer;
  > img {
    width: 48px;
    height: 48px;
  }
}
