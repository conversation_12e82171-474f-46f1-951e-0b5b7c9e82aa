import { useState, useEffect, useCallback, useRef } from "react";
import {
  showPop,
  closePop,
  reportEXP,
  reportCLOSE,
  reportCLK,
} from "@ali/pc-pop-sdk";
import CouponItem from "./CouponItem";
import { requestWrite } from "./request";
import { appendParamsToUrl, isTrue } from "@ali/pcom-tbpc-venue-utils";
import PopMask from '../../components/MaskContainer';
// import { doubleRedpacketMock } from "../../mock/data";

interface IPopUIData {
  hairTitle: string;
  displayAmount: string;
  subTitle: string;
  desc: string;
  action: string;
  actionUrl: string;
  bgImage: string;
  spmD: string;
  headerTitle: string;
}

const FROM_SURPRISE_DOUBLE_RED_PACKET_SPM_D = "d_surpriseConsumerCoupon";

export default function TbpcBuyBackPop(props: any) {
  const { page, template, activity } = props || {};
  const {
    asac = "2A24514KMM6TWY02RE4CPQ",
    channel = "lafite_PCjingxihongbao",
    strategyCode = null,
    bgImage = "",
    hairTitle = '',
    action = '',
    jumpUrl = '',
    desc = '',
  } = page?.content || {};

  const { displayStyle = "window", templateCode } = template || {};
  const { sceneCode = "", activityId } = activity || {};
  const [rewardData, setRewardData] = useState<IPopUIData>();
  const fromRef = useRef("");
  const clickSpmD = `d_${activityId}`;

  const showPopHandler = useCallback(() => {
    showPop({
      displayStyle,
      sceneCode,
    });
    reportEXP({
      displayStyle,
      sceneCode,
      selfDefine: {
        from: fromRef.current,
      },
    });
  }, []);

  const fetchPop = useCallback(async (params: any) => {
    const { asac, channel } = params || {};
    const readWriteRes = await requestWrite({
      bizCode: "buy_back",
      bizParams: JSON.stringify({
        action: "jingxi_hb_pop_issue",
        channel: channel,
        strategyCode: strategyCode
      }),
      asac,
    }, props);
    // const readWriteRes =  isTrue(_readWriteRes?.success) ? _readWriteRes : doubleRedpacketMock?.data
    // console.log('readWriteRes', readWriteRes)
    // 红包数量大于1，且请求成功
    if (readWriteRes?.data?.benefits?.length >= 1 && isTrue(readWriteRes?.success)) {
      // 新增惊喜红包状态code surpriseConsumerCoupon
      if (templateCode === "surpriseConsumerCoupon") {
        fromRef.current = FROM_SURPRISE_DOUBLE_RED_PACKET_SPM_D;
        setRewardData({
          ...readWriteRes?.data,
          bgImage,
          action,
          jumpUrl,
          hairTitle,
          desc,
          actionClickHandler: () => {
            clickPopHandler();
          },
          spmD: clickSpmD,
        });
        showPopHandler();
        return;
      }
    }
  }, []);

  const closePopHandler = useCallback(() => {
    closePop({
      displayStyle,
      sceneCode,
    });
    reportCLOSE({
      displayStyle,
      sceneCode,
      selfDefine: {
        from: fromRef.current,
      },
    });
  }, []);

  const clickPopHandler = useCallback(() => {
    reportCLK({
      displayStyle,
      sceneCode,
      selfDefine: {
        from: fromRef.current,
      },
    });
    closePop({
      displayStyle,
      sceneCode,
    });
  }, []);

  useEffect(() => {
    if (templateCode === "surpriseConsumerCoupon") {
      fetchPop({
        asac,
        channel
      });
    }
  }, []);

  return rewardData ? (
    <PopMask>
      <CouponItem
        {...rewardData}
        spmD={clickSpmD}
        closeHandler={closePopHandler}
      />
    </PopMask>
  ) : null;
}
