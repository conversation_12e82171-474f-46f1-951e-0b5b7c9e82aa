import { isTrue, mtopRequest } from "@ali/pcom-tbpc-venue-utils";
import { reportRequestLogOnFail } from '@ali/pc-pop-sdk';

interface IRedPackageParams {
    bizCode: string;
    bizParams: string;
    asac: string;
}
interface IRedPackageRes {
    success: boolean;
    message: string;
    [key: string]: any;
}

export const requestRead = async ({
    bizCode,
    bizParams,
    asac,
}: IRedPackageParams, props: any = {}): Promise<IRedPackageRes> => {
    const requestParams = {
        bizCode,
        bizParams,
        asac,
    };
    const api = "mtop.mktcell.gateway.read";
    try {
        const res = await mtopRequest({
            api,
            data: requestParams,
        });
        if (isTrue(res.success)) {
            return {
                data: res?.data || {},
                message: "",
                success: true,
            };
        }
        reportRequestLogOnFail({
            props,
            args: {
                req: requestParams,
                res,
                traceId: res.traceId,
                code: res?.errorCode || res?.code,
                msg: res?.message || res?.msg,
                api,
            },
        });
        return {
            data: {},
            message: "",
            success: false,
        };
    } catch (err) {
        console.error("mtop.mktcell.gateway.read failed:", err);
        return {
            data: err,
            message: err?.message || '',
            success: false,
        };
    }
};

export const requestWrite = async ({
    bizCode,
    bizParams,
    asac,
}: IRedPackageParams, props: any = {}): Promise<IRedPackageRes> => {
    const requestParams = {
        bizCode,
        bizParams,
        asac,
    };
    const api = "mtop.mktcell.gateway.write";
    try {
        const res = await mtopRequest({
            api,
            data: requestParams,
        });
        if (isTrue(res?.success)) {
            return res || {};
        } else {
            reportRequestLogOnFail({
                props,
                args: {
                    req: requestParams,
                    res,
                    traceId: res.traceId,
                    code: res?.errorCode || res?.code,
                    msg: res?.message || res?.msg,
                    api,
                },
            });
            throw res;
        }
    } catch (err) {
        console.error("mtop.mktcell.gateway.write:", err);
        return {
            data: err,
            message: err?.message || '',
            success: false,
        };
    }
};
