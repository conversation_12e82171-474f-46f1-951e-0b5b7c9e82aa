// 降价提醒pop mock
export const gapMock = [
  {
    price: 441.26,
    gap: 116.74,
    title: '2024新款爆款獭兔毛皮草外套女皮毛一体毛毛外套女冬季毛绒大衣',
    pic: '//gw.alicdn.com/imgextra/i2/72046587/O1CN01Dvy1eo1yWtkwH6mst_!!72046587.jpg',
  },
  {
    price: 917,
    gap: 50,
    title: '【中瑕】COACH 蔻驰女士山茶花斜挎包 C4659卡其棕配黑色B4M2',
    pic: '//gw.alicdn.com/imgextra/i3/3283840015/O1CN01S11R5L1ByutiFzX8I_!!3283840015.png',
  },
  {
    price: 449,
    gap: 10,
    title: 'WD西数移动固态硬盘1T电脑笔记本2T手机小巧高速小饼干西部数据',
    pic: '//gw.alicdn.com/imgextra/i1/2037117585/O1CN01O7M2Cx25tzARAarQP_!!2037117585.jpg',
  },
  {
    price: 43.75,
    gap: 2,
    title: '纯棉花幼儿园床垫儿童褥子婴儿全棉垫被小学生午睡垫四季加厚定做',
    pic: '//gw.alicdn.com/imgextra/i3/2448248392/O1CN01TJ4z5u2Braik2MH45_!!0-item_pic.jpg',
  },
  {
    price: 38.71,
    gap: 1.19,
    title: '优选水果干混合自选冻干草莓水蜜桃无花果芒果菠萝蜜脆散装袋子装',
    pic: '//gw.alicdn.com/imgextra/i3/248457482/O1CN01D4e182258oMLY6BcW_!!248457482.jpg',
  },
];

// 低价商品pop mock
export const itemsMock = [
  {
    currentAldResId: '36853669',
    type: 'item',
    utparam: {
      floorId: '41614176',
      recIndex: '13',
      x_object_type: 'item',
      pvid: '9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad',
      x_item_ids: '************',
      scm: '1007.10302.411105.401086_0_0',
      x_object_id: '************',
      tpp_buckets: '302#0#411105#0_30636#0#411105#18',
    },
    cvrScore: '0.00186759',
    distinctId: '************',
    trackParams: {
      trackInfo:
        '/sea.hangye.floor-tm-----scm=1007.10302.411105.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utLogMap=%7B%22recIndex%22%3A13%2C%22x_hestia_source%22%3A%22tm_fen_floor%22%2C%22x_object_type%22%3A%22item%22%2C%22algo_layer_ext%22%3A%22%22%2C%22calibrationScore%22%3A%22%22%2C%22x_hestia_subsource%22%3A%22default%22%2C%22wh_pid%22%3A-1%2C%22x_hestia_flow%22%3A41614176%2C%22x_hestia_rtp_biz_score%22%3A%22411105%23zx_dacu_exhibition_aware_dacu_louceng_column_v0%230.00717685%3B411105%23xiugou_dacu_mainexpo_feeds_cvr_idr_mix%230.00186759%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22x_item_ids%22%3A************%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22x_algo_paras%22%3A%221007.10302.411105.401086_0_0%3A9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%3A2215127013238%3Atm_fen_floor%3A-1%3A%3A%3Aitem%3Apv%3A0%3Adefault-___************%3A8%3A0.00001%3A12%3A41614176%3A0.00718%3A0.00187%3A608.00%3A0.59099%3A0.0%3A1%3A619%3A0%3A0%3A109392320-0-0-0%3A0-0-0-0-0-0-0-0-42.120.74.237-0-0-0-0-12574478-0%3A-0%3A45.00-3.00-0.00000-0.00000-0-1-1.00-0.00000%3Amock-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A%3AA0-B0-C0-D0-E0-0-0-0-0%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22x_object_id%22%3A************%7D-----GET',
    },
    ctrScore: '0.00717685',
    __track__: '36853669.36853669.47679058.4984.1',
    shortTitleWithTmc: '拼接水洗夹克',
    venueFuzzyMonthSellNum: '已售100+件',
    whiteImg:
      '//img.alicdn.com/bao/upload/O1CN01oGv68D1TNy6LqKJzh_!!6000000002371-0-yinhe.jpg',
    benefit: '每200减20',
    itemId: '************',
    dataSetId: '43357030',
    dataSetType: '2',
    _mt_: {
      itemTitle8:
        'res:36853669;s:yinheItem;kmf:shortTitleWithTmc;mid:122317762636;kyh:itemTitle8;kef:shortTitleWithTmc',
      sc_item_title_10:
        'res:36853669;s:yinheItem;kmf:shortTitleWithTmc;mid:122317766714;kyh:sc_item_title_10;kef:shortTitleWithTmc',
      itemWhiteImg:
        'res:36853669;s:yinheItem;kmf:whiteImg;mid:122317776580;kyh:itemWhiteImg;kef:whiteImg',
      sellPoint1:
        'res:36853669;s:yinheItem;kmf:benefit;mid:122317764730;kyh:sellPoint1;kef:benefit',
      sc_item_img_800_800:
        'res:36853669;s:yinheItem;kmf:whiteImg;mid:122317778366;kyh:sc_item_img_800_800;kef:whiteImg',
    },
    pcDiscountPrice: '608',
    itemUrl:
      '//detail.tmall.com/item.htm?id=************&scm=1007.10302.274556.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22recIndex%22%3A13%2C%22x_item_ids%22%3A************%2C%22x_object_id%22%3A************%7D&xxc=promotionVenue&skuId=5722608805657',
    __pos__: '1',
  },
  {
    currentAldResId: '36853669',
    type: 'item',
    utparam: {
      floorId: '41614176',
      recIndex: '14',
      x_object_type: 'item',
      pvid: '9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad',
      x_item_ids: '************',
      scm: '1007.10302.411105.401086_0_0',
      x_object_id: '************',
      tpp_buckets: '302#0#411105#0_30636#0#411105#18',
    },
    cvrScore: '0.00643811',
    distinctId: '************',
    trackParams: {
      trackInfo:
        '/sea.hangye.floor-tm-----scm=1007.10302.411105.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utLogMap=%7B%22recIndex%22%3A14%2C%22x_hestia_source%22%3A%22tm_fen_floor%22%2C%22x_object_type%22%3A%22item%22%2C%22algo_layer_ext%22%3A%22%22%2C%22calibrationScore%22%3A%22%22%2C%22x_hestia_subsource%22%3A%22default%22%2C%22wh_pid%22%3A-1%2C%22x_hestia_flow%22%3A41614176%2C%22x_hestia_rtp_biz_score%22%3A%22411105%23zx_dacu_exhibition_aware_dacu_louceng_column_v0%230.00612128%3B411105%23xiugou_dacu_mainexpo_feeds_cvr_idr_mix%230.00643811%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22x_item_ids%22%3A************%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22x_algo_paras%22%3A%221007.10302.411105.401086_0_0%3A9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%3A2215127013238%3Atm_fen_floor%3A-1%3A%3A%3Aitem%3Apv%3A0%3Adefault-___************%3A8%3A0.00001%3A13%3A41614176%3A0.00612%3A0.00644%3A68.00%3A0.61122%3A0.0%3A1%3A37%3A0%3A0%3A109392320-0-0-0%3A0-0-0-0-0-0-0-0-42.120.74.237-0-0-0-0-12574478-0%3A-0%3A45.00-3.00-0.00000-0.00000-0-1-1.00-0.00000%3Amock-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A%3AA0-B0-C0-D0-E0-0-0-0-0%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22x_object_id%22%3A************%7D-----GET',
    },
    ctrScore: '0.00612128',
    __track__: '36853669.36853669.47679058.4984.2',
    shortTitleWithTmc: '红色粗跟高跟鞋',
    venueFuzzyMonthSellNum: '已售700+件',
    whiteImg:
      '//img.alicdn.com/bao/upload/O1CN01dZrOAv1UbXDiSmMFo_!!6000000002536-0-yinhe.jpg',
    benefit: '每200减20',
    itemId: '************',
    dataSetId: '43357030',
    dataSetType: '2',
    _mt_: {
      itemTitle8:
        'res:36853669;s:yinheItem;kmf:shortTitleWithTmc;mid:111708398340;kyh:itemTitle8;kef:shortTitleWithTmc',
      itemWhiteImg:
        'res:36853669;s:yinheItem;kmf:whiteImg;mid:109337999120;kyh:itemWhiteImg;kef:whiteImg',
      sc_item_img_800_800:
        'res:36853669;s:yinheItem;kmf:whiteImg;mid:109345405508;kyh:sc_item_img_800_800;kef:whiteImg',
    },
    pcDiscountPrice: '68',
    itemUrl:
      '//detail.tmall.com/item.htm?id=************&scm=1007.10302.274556.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22recIndex%22%3A14%2C%22x_item_ids%22%3A************%2C%22x_object_id%22%3A************%7D&xxc=promotionVenue&skuId=5096447019059',
    __pos__: '2',
  },
  {
    currentAldResId: '36853669',
    type: 'item',
    utparam: {
      floorId: '41614176',
      recIndex: '15',
      x_object_type: 'item',
      pvid: '9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad',
      x_item_ids: '************',
      scm: '1007.10302.411105.401086_0_0',
      x_object_id: '************',
      tpp_buckets: '302#0#411105#0_30636#0#411105#18',
    },
    cvrScore: '0.00285599',
    distinctId: '************',
    trackParams: {
      trackInfo:
        '/sea.hangye.floor-tm-----scm=1007.10302.411105.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utLogMap=%7B%22recIndex%22%3A15%2C%22x_hestia_source%22%3A%22tm_fen_floor%22%2C%22x_object_type%22%3A%22item%22%2C%22algo_layer_ext%22%3A%22%22%2C%22calibrationScore%22%3A%22%22%2C%22x_hestia_subsource%22%3A%22default%22%2C%22wh_pid%22%3A-1%2C%22x_hestia_flow%22%3A41614176%2C%22x_hestia_rtp_biz_score%22%3A%22411105%23zx_dacu_exhibition_aware_dacu_louceng_column_v0%230.00614506%3B411105%23xiugou_dacu_mainexpo_feeds_cvr_idr_mix%230.00285599%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22x_item_ids%22%3A************%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22x_algo_paras%22%3A%221007.10302.411105.401086_0_0%3A9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%3A2215127013238%3Atm_fen_floor%3A-1%3A%3A%3Aitem%3Apv%3A0%3Adefault-___************%3A8%3A0.00000%3A14%3A41614176%3A0.00615%3A0.00286%3A139.00%3A0.59393%3A0.0%3A1%3A938%3A0%3A0%3A109392320-0-0-0%3A0-0-0-0-0-0-0-0-42.120.74.237-0-0-0-0-12574478-0%3A-0%3A45.00-3.00-0.00000-0.00000-0-1-1.00-0.00000%3Amock-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A%3AA0-B0-C0-D0-E0-0-0-0-0%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22x_object_id%22%3A************%7D-----GET',
    },
    ctrScore: '0.00614506',
    __track__: '36853669.36853669.47679058.4984.3',
    shortTitleWithTmc: '风外套女新款上衣',
    venueFuzzyMonthSellNum: '已售100+件',
    whiteImg:
      '//gju4.alicdn.com/tps/i3/153692198/O1CN014ndahU1S6jZvYIUvn_!!153692198.jpg',
    benefit: '每200减20',
    itemId: '************',
    dataSetId: '43357030',
    dataSetType: '2',
    _mt_: {
      sc_item_title_10:
        'res:36853669;s:yinheItem;kmf:shortTitleWithTmc;mid:121831100072;kyh:sc_item_title_10;kef:shortTitleWithTmc',
    },
    pcDiscountPrice: '139',
    itemUrl:
      '//detail.tmall.com/item.htm?id=************&scm=1007.10302.274556.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22recIndex%22%3A15%2C%22x_item_ids%22%3A************%2C%22x_object_id%22%3A************%7D&xxc=promotionVenue&skuId=5872288338306',
    __pos__: '3',
  },
  {
    currentAldResId: '36853669',
    type: 'item',
    utparam: {
      floorId: '41614176',
      recIndex: '16',
      x_object_type: 'item',
      pvid: '9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad',
      x_item_ids: '************',
      scm: '1007.10302.411105.401086_0_0',
      x_object_id: '************',
      tpp_buckets: '302#0#411105#0_30636#0#411105#18',
    },
    cvrScore: '0.00439948',
    distinctId: '************',
    trackParams: {
      trackInfo:
        '/sea.hangye.floor-tm-----scm=1007.10302.411105.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utLogMap=%7B%22recIndex%22%3A16%2C%22x_hestia_source%22%3A%22tm_fen_floor%22%2C%22x_object_type%22%3A%22item%22%2C%22algo_layer_ext%22%3A%22%22%2C%22calibrationScore%22%3A%22%22%2C%22x_hestia_subsource%22%3A%22default%22%2C%22wh_pid%22%3A-1%2C%22x_hestia_flow%22%3A41614176%2C%22x_hestia_rtp_biz_score%22%3A%22411105%23zx_dacu_exhibition_aware_dacu_louceng_column_v0%230.00526083%3B411105%23xiugou_dacu_mainexpo_feeds_cvr_idr_mix%230.00439948%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22x_item_ids%22%3A************%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22x_algo_paras%22%3A%221007.10302.411105.401086_0_0%3A9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%3A2215127013238%3Atm_fen_floor%3A-1%3A%3A%3Aitem%3Apv%3A0%3Adefault-___************%3A8%3A0.00000%3A15%3A41614176%3A0.00526%3A0.00440%3A128.00%3A0.58541%3A0.0%3A1%3A305%3A0%3A0%3A109392320-0-0-0%3A0-0-0-0-0-0-0-0-42.120.74.237-0-0-0-0-12574478-0%3A-0%3A45.00-3.00-0.00000-0.00000-0-1-1.00-0.00000%3Amock-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A%3AA0-B0-C0-D0-E0-0-0-0-0%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22x_object_id%22%3A************%7D-----GET',
    },
    ctrScore: '0.00526083',
    __track__: '36853669.36853669.47679058.4984.4',
    shortTitleWithTmc:
      '应小姐【赛博辣妹】美式复古字母印花T恤女微胖mm长袖打底衫上衣',
    venueFuzzyMonthSellNum: '已售100+件',
    whiteImg:
      '//gju2.alicdn.com/tps/i4/2209885943732/O1CN01EipRO61dRJ6DoDMYu_!!2209885943732.jpg',
    benefit: '每200减20',
    itemId: '************',
    dataSetId: '43357030',
    dataSetType: '2',
    pcDiscountPrice: '120',
    itemUrl:
      '//detail.tmall.com/item.htm?id=************&scm=1007.10302.274556.401086_0_0&pvid=9d30e68f-bfe6-4cf3-88b4-0f9f44c7efad&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22tpp_buckets%22%3A%22302%230%23411105%230_30636%230%23411105%2318%22%2C%22floorId%22%3A41614176%2C%22pvid%22%3A%229d30e68f-bfe6-4cf3-88b4-0f9f44c7efad%22%2C%22scm%22%3A%221007.10302.411105.401086_0_0%22%2C%22recIndex%22%3A16%2C%22x_item_ids%22%3A************%2C%22x_object_id%22%3A************%7D&xxc=promotionVenue&skuId=5712885482182',
    __pos__: '4',
  },
];

// 侧边抽屉静态资源位pop mock
export const sliderDrawerMock = {
  page: {
    content: {
      bgImageBefore:
        'https://img.alicdn.com/imgextra/i3/O1CN01g226ak1RZIfTTXTbe_!!6000000002125-0-tps-234-756.jpg',
      bgImageAfter:
        'https://img.alicdn.com/imgextra/i2/O1CN01sqwblT1uM88Qz5cYM_!!6000000006022-0-tps-750-480.jpg',
      jumpUrl: 'https://www.taobao.com',
    },
  },
  activity: {
    activityId: 123,
    sceneCode: 'home',
  },
  template: {
    displayStyle: 'drawer',
  },
};

// 侧边挂件资源位pop mock
export const slideStaticMock = {
  page: {
    content: {
      bgImageBefore:
        'https://img.alicdn.com/imgextra/i2/O1CN01sqwblT1uM88Qz5cYM_!!6000000006022-0-tps-750-480.jpg',
      displayPosition: 'right',
      jumpUrl: 'https://www.taobao.com',
    },
  },
  activity: {
    activityId: 123,
    sceneCode: 'home',
  },
  template: {
    displayStyle: 'float',
    attributes: {
      distanceToBottom: 20,
    },
  },
};

// 催用提醒弹窗券数据mock
export const reminderCouponMock = {
  api: 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView',
  attributes: {
    serverTimeMs: 1739254537170,
    traceId: '213e3f9e17392545370488118e1b97',
    timeShuttle: false,
    serverTime: '2025-02-11 14:15:37',
    env: 'pre',
  },
  code: 200,
  errorCode: 'SUCCESS',
  message: '调用成功',
  value: {
    benefits: [
      {
        amountThreshold: 40000,
        // 资产类型：coupon-优惠券
        assetType: 'coupon',
        currentTimestamp: 1739254537165,
        // 门槛
        displayAmountThreshold: '400',
        // 面额
        displayTotalFee: '40',
        // 跳转凑单神器所需参数
        extraData: {
          couponTag: '2240640002',
          templateCode: 12886011008,
        },
        gmtInvalid: '2025-02-14 23:59:59',
        // 失效时间戳
        invalidTimestamp: 1741325435000,
        title: '运动户外',
        totalFee: 4000,
      },
      {
        amountThreshold: 19900,
        assetType: 'coupon',
        currentTimestamp: 1739254537165,
        displayAmountThreshold: '199',
        displayTotalFee: '20',
        extraData: {
          couponTag: '2240040001',
          templateCode: 12868704380,
        },
        gmtInvalid: '2025-02-14 23:59:59',
        invalidTimestamp: 1741625435000,
        title: '美食惊喜',
        totalFee: 2000,
      },
    ],
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};

// 催用提醒弹窗红包数据mock
export const reminderRedPacketMock = {
  api: 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView',
  attributes: {
    serverTimeMs: 1739254537170,
    traceId: '213e3f9e17392545370488118e1b97',
    timeShuttle: false,
    serverTime: '2025-02-11 14:15:37',
    env: 'pre',
  },
  code: 200,
  errorCode: 'SUCCESS',
  message: '调用成功',
  value: {
    benefits: [
      {
        amountThreshold: 19900,
        assetType: 'redPacket',
        currentTimestamp: 1739254537165,
        displayAmountThreshold: '199',
        displayTotalFee: '20',
        extraData: {
          couponTag: '2235470001',
          templateCode: 12741552449,
        },
        gmtInvalid: '2025-02-14 23:59:59',
        invalidTimestamp: 1739548799000,
        title: '美食惊喜',
        totalFee: 2000,
      },
    ],
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};

// 双红包数据
export const doubleRedpacketMock = {
  "api": "mtop.mktcell.gateway.write",
  "data": {
      "attributes": {
          "serverTime": 1745220568891
      },
      "data": {
          "benefits": [
              {
                  "displayAmount": "5",
                  "benefitTitle": "惊喜红包",
                  "issueTime": 1745220568754,
                  "benefitType": "fpRedEnvelope",
                  "effectiveStartTimestamp": 1745409600000,
                  "displayStartFee": "50",
                  "effectiveEndTimestamp": 1745855999000,
                  "sendStatus": "success",
                  "benefitCode": "6536b39994344f39b5a5c4ec2e006a7d"
              },
              {
                  "displayAmount": "15",
                  "benefitTitle": "惊喜红包",
                  "issueTime": 1745220568067,
                  "benefitType": "fpRedEnvelope",
                  "effectiveStartTimestamp": 1745409600000,
                  "displayStartFee": "200",
                  "effectiveEndTimestamp": 1745855999000,
                  "sendStatus": "success",
                  "benefitCode": "b1e70a92f3734059a3024c4df9fc6a94"
              }
          ],
          "totalDisplayAmount": "20"
      },
      "success": true
  },
  "ret": [
      "SUCCESS::调用成功"
  ],
  "traceId": "213e3f9e17452205677124887e1bc1",
  "v": "1.0"
}


export const popData = {
  "api": "mtop.alibaba.fc.api.maoxland.containerfacade.singleview",
  "data": {
      "attributes": {
          "serverTimeMs": 1745202147301,
          "traceId": "213e3f9c17452021472663378e1e6c",
          "timeShuttle": false,
          "serverTime": "2025-04-21 10:22:27",
          "env": "pre"
      },
      "code": 200,
      "debug": {
          "环境信息": [
              "debug 默认开启 环境 pre",
              {
                  "traceId": "213e3f9c17452021472663378e1e6c",
                  "hostName": "fc-meta-front033062001215.pre.na610",
                  "site": "na610",
                  "unit": "pre",
                  "appGroupName": "fc-meta-front_prehost",
                  "appName": "fc-meta-front",
                  "ip": "***********"
              },
              "logUrl: null "
          ],
          "入参": [
              {
                  "debug": false,
                  "mockUserId": 0,
                  "msgCode": "PcTaobaoPopQuery",
                  "params": {
                      "sceneCode": "home",
                      "fromUrl": "https://pre-wormhole.wapa.tmall.com/wow/z/tbhome/taobao/SLK24QOYJV?jingxihongbao_test=1"
                  },
                  "projectName": "PcTaobao",
                  "responseCode": "PcTaobaoPopQuery",
                  "source": "mtop",
                  "userId": 2215127013239,
                  "userNick": "逾歌测试02"
              },
              {}
          ],
          "协议分发": [
              "view: PcTaobaoPopQuery ,订阅查找 key PcTaobao_PcTaobaoPopQuery , listenerSize 1",
              "降级 msgCode PcTaobaoPopQuery roomId null subKey sceneCode subValue home 降级状态 false ",
              "降级 msgCode PcTaobaoPopQuery  projectName PcTaobao 降级状态 false ",
              "#降级 msgCode PcTaobaoPopQuery 降级状态 false ",
              "自定义限流,SentinelParamKey sceneCode,sentinelParamValuehome",
              "限流 resourceName view:PcTaobaoPopQuery:sceneCode:home 执行！"
          ],
          "协议执行#PcTaobaoPopQueryExecutor:PcTaobaoPopQuery": [
              {
                  "活动-524": [
                      {
                          "活动配置": [
                              {
                                  "activityDesc": "五棱镜倒计时任务pop_首页",
                                  "activityId": 524,
                                  "activityName": "五棱镜倒计时任务pop_首页",
                                  "endTime": 4930646399000,
                                  "env": "online",
                                  "interruptIfFatigue": 0,
                                  "pageId": 268,
                                  "priority": 99999999,
                                  "sceneCode": "home",
                                  "startTime": 1739154168000,
                                  "status": 1,
                                  "supportedMethod": "sequential"
                              }
                          ],
                          "页面配置": [
                              {
                                  "pageDesc": "",
                                  "pageId": 268,
                                  "pageName": "五棱镜倒计时任务pop",
                                  "templateCode": "WLJCountDownTaskPop"
                              }
                          ],
                          "模板配置": [
                              {
                                  "displayStyle": "float",
                                  "source": "custom",
                                  "status": 1,
                                  "templateCode": "WLJCountDownTaskPop",
                                  "templateDesc": "五棱镜倒计时任务pop",
                                  "templateName": "五棱镜倒计时任务pop"
                              }
                          ],
                          "弹窗规则不符合": [
                              "规则配置=PopRuleCfg(ruleId=546, ruleName=五棱镜倒计时任务pop, ruleDesc=五棱镜倒计时任务pop, activityId=524, ruleType=url_param, ruleParams=[taskPopup=true], matchStrategy=and) "
                          ],
                          "规则匹配": [
                              "规则匹配结果:false"
                          ],
                          "耗时": 0
                      }
                  ],
                  "活动-516": [
                      {
                          "活动配置": [
                              {
                                  "activityDesc": "淘金币右侧异形",
                                  "activityId": 516,
                                  "activityName": "淘金币右侧异形",
                                  "endTime": 1761926399000,
                                  "env": "online",
                                  "interruptIfFatigue": 0,
                                  "pageId": 263,
                                  "priority": 999999,
                                  "sceneCode": "home",
                                  "startTime": 1738890000000,
                                  "status": 1
                              }
                          ],
                          "页面配置": [
                              {
                                  "pageDesc": "淘金币右pop",
                                  "pageId": 263,
                                  "pageName": "淘金币右pop",
                                  "templateCode": "slideStaticResPop"
                              }
                          ],
                          "模板配置": [
                              {
                                  "displayStyle": "float",
                                  "source": "custom",
                                  "status": 1,
                                  "templateCode": "slideStaticResPop",
                                  "templateDesc": "可左可右，下间距由技术控制",
                                  "templateName": "下方两侧静态资源位"
                              }
                          ],
                          "规则匹配": [
                              "规则匹配结果:true"
                          ],
                          "疲劳度判断": [
                              "get，cacheKey = NF#pop_a_516#d_20250421#pre#2215127013239，count = 0 ",
                              "judge，context = FatigueContext(fatiguePrefix=pop_a_516, fatigueType=nature, fatigueInterval=null, fatigueCycle=DAY, fatigueLimit=99, entityId=2215127013239, incr=false, incrValue=null)，result = true "
                          ],
                          "疲劳度消耗": [
                              "incrAndGet，cacheKey = NF#pop_a_516#d_20250421#pre#2215127013239，count = 1 ",
                              "incr，context = FatigueContext(fatiguePrefix=pop_a_516, fatigueType=nature, fatigueInterval=null, fatigueCycle=DAY, fatigueLimit=99, entityId=2215127013239, incr=false, incrValue=null)，result = 1 "
                          ],
                          "疲劳度控制": [
                              "疲劳度控制结果:true"
                          ],
                          "耗时": 4
                      }
                  ],
                  "活动-667": [
                      {
                          "活动配置": [
                              {
                                  "activityId": 667,
                                  "activityName": "惊喜红包测试活动",
                                  "endTime": 1745245002000,
                                  "env": "pre",
                                  "interruptIfFatigue": 0,
                                  "pageId": 382,
                                  "priority": 99999999,
                                  "sceneCode": "home",
                                  "startTime": 1744874194000,
                                  "status": 1
                              }
                          ],
                          "页面配置": [
                              {
                                  "pageId": 382,
                                  "pageName": "惊喜红包测试",
                                  "templateCode": "surpriseConsumerCoupon"
                              }
                          ],
                          "模板配置": [
                              {
                                  "displayStyle": "window",
                                  "source": "custom",
                                  "status": 1,
                                  "templateCode": "surpriseConsumerCoupon",
                                  "templateDesc": "两个红包玩法，2025年4月大促开发",
                                  "templateName": "惊喜红包（两个红包玩法）"
                              }
                          ],
                          "规则匹配": [
                              "规则匹配结果:true"
                          ],
                          "疲劳度判断": [
                              "get，cacheKey = NF#pop_a_667#d_20250421#pre#2215127013239，count = 0 ",
                              "judge，context = FatigueContext(fatiguePrefix=pop_a_667, fatigueType=nature, fatigueInterval=null, fatigueCycle=DAY, fatigueLimit=999, entityId=2215127013239, incr=false, incrValue=null)，result = true "
                          ],
                          "疲劳度消耗": [
                              "incrAndGet，cacheKey = NF#pop_a_667#d_20250421#pre#2215127013239，count = 1 ",
                              "incr，context = FatigueContext(fatiguePrefix=pop_a_667, fatigueType=nature, fatigueInterval=null, fatigueCycle=DAY, fatigueLimit=999, entityId=2215127013239, incr=false, incrValue=null)，result = 1 "
                          ],
                          "疲劳度控制": [
                              "疲劳度控制结果:true"
                          ],
                          "耗时": 8
                      }
                  ],
                  "MetaEventListener-req": [
                      {
                          "debug": false,
                          "mockUserId": 0,
                          "msgCode": "PcTaobaoPopQuery",
                          "params": {
                              "sceneCode": "home",
                              "fromUrl": "https://pre-wormhole.wapa.tmall.com/wow/z/tbhome/taobao/SLK24QOYJV?jingxihongbao_test=1"
                          },
                          "projectName": "PcTaobao",
                          "responseCode": "PcTaobaoPopQuery",
                          "source": "mtop",
                          "userId": 2215127013239,
                          "userNick": "逾歌测试02"
                      }
                  ],
                  "MetaEventListener-resp": [
                      {
                          "attributes": {
                              "serverTimeMs": 1745202147301,
                              "traceId": "213e3f9c17452021472663378e1e6c",
                              "timeShuttle": false,
                              "serverTime": 1745202147301,
                              "env": "pre"
                          },
                          "data": {
                              "popList": [
                                  {
                                      "activity": {
                                          "activityId": 516,
                                          "activityName": "淘金币右侧异形",
                                          "endTime": 1761926399000,
                                          "pageId": 263,
                                          "priority": 999999,
                                          "sceneCode": "home",
                                          "startTime": 1738890000000,
                                          "status": 1
                                      },
                                      "callback": {
                                          "shouldCallback": true,
                                          "shouldTrack": true,
                                          "track": {
                                              "common": {
                                                  "activityId": 516,
                                                  "dispatchId": "f10d08ad2b2340fc907b75d27f46d793",
                                                  "loginStatus": 1,
                                                  "pageId": 263,
                                                  "sceneCode": "home",
                                                  "templateCode": "slideStaticResPop",
                                                  "traceId": "213e3f9c17452021472663378e1e6c"
                                              },
                                              "custom": {
                                                  "custom_content_source": "pop_516",
                                                  "env": "pre"
                                              }
                                          }
                                      },
                                      "page": {
                                          "content": {
                                              "bgImageBefore": "https://gw.alicdn.com/imgextra/i3/O1CN01BbaRCe1tJYIZiPH0A_!!6000000005881-2-tps-640-320.png",
                                              "displayPosition": "right",
                                              "jumpUrl": "https://huodong.taobao.com/wow/z/tbhome/pc-growth/tao-coin"
                                          },
                                          "pageDesc": "淘金币右pop",
                                          "pageId": 263,
                                          "pageName": "淘金币右pop",
                                          "templateCode": "slideStaticResPop"
                                      },
                                      "template": {
                                          "attributes": {
                                              "cdnjs": "https://g.alicdn.com/code/npm/@ali/tbpc-pop-dynamic-common/1.0.0/index.umd.es5.production.js",
                                              "distanceToBottom": "15"
                                          },
                                          "displayStyle": "float",
                                          "source": "custom",
                                          "templateCode": "slideStaticResPop",
                                          "templateDesc": "可左可右，下间距由技术控制",
                                          "templateName": "下方两侧静态资源位"
                                      },
                                      "track": {
                                          "activityId": 516,
                                          "activityName": "淘金币右侧异形",
                                          "dispatchId": "f10d08ad2b2340fc907b75d27f46d793",
                                          "loginStatus": 1,
                                          "pageId": 263,
                                          "sceneCode": "home",
                                          "templateCode": "slideStaticResPop",
                                          "traceId": "213e3f9c17452021472663378e1e6c"
                                      }
                                  },
                                  {
                                      "activity": {
                                          "activityId": 667,
                                          "activityName": "惊喜红包测试活动",
                                          "endTime": 1745245002000,
                                          "pageId": 382,
                                          "priority": 99999999,
                                          "sceneCode": "home",
                                          "startTime": 1744874194000,
                                          "status": 1
                                      },
                                      "callback": {
                                          "shouldCallback": true,
                                          "shouldTrack": true,
                                          "track": {
                                              "common": {
                                                  "activityId": 667,
                                                  "dispatchId": "bb3058727ed54e028da240f790b5aabe",
                                                  "loginStatus": 1,
                                                  "pageId": 382,
                                                  "sceneCode": "home",
                                                  "templateCode": "surpriseConsumerCoupon",
                                                  "traceId": "213e3f9c17452021472663378e1e6c"
                                              },
                                              "custom": {
                                                  "custom_content_source": "pop_667",
                                                  "env": "pre"
                                              }
                                          }
                                      },
                                      "page": {
                                          "content": {
                                              "hairTitle": "惊喜红包",
                                              "channel": "lafite_PChongbao",
                                              "asac": "2A24514KMM6TWY02RE4CPQ",
                                              "action": "开心收下",
                                              "bgImage": "https://img.alicdn.com/imgextra/i3/O1CN01tnKRT01yZeJLP2TE2_!!6000000006593-2-tps-640-764.png",
                                              "jumpUrl": "https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-561441",
                                              "desc": "可叠加立减折上折"
                                          },
                                          "pageId": 382,
                                          "pageName": "惊喜红包测试",
                                          "templateCode": "surpriseConsumerCoupon"
                                      },
                                      "template": {
                                          "attributes": {
                                              "cdnjs": "https://dev.g.alicdn.com/code/npm/@ali/tbpc-pop-dynamic-common/1.0.7/index.umd.es5.development.js"
                                          },
                                          "displayStyle": "window",
                                          "source": "custom",
                                          "templateCode": "surpriseConsumerCoupon",
                                          "templateDesc": "两个红包玩法，2025年4月大促开发",
                                          "templateName": "惊喜红包（两个红包玩法）"
                                      },
                                      "track": {
                                          "activityId": 667,
                                          "activityName": "惊喜红包测试活动",
                                          "dispatchId": "bb3058727ed54e028da240f790b5aabe",
                                          "loginStatus": 1,
                                          "pageId": 382,
                                          "sceneCode": "home",
                                          "templateCode": "surpriseConsumerCoupon",
                                          "traceId": "213e3f9c17452021472663378e1e6c"
                                      }
                                  }
                              ],
                              "popup": true
                          },
                          "expectedError": false,
                          "responseCode": "SUCCESS",
                          "success": true
                      }
                  ],
                  "耗时": 15
              }
          ],
          "耗时": 15
      },
      "errorCode": "SUCCESS",
      "message": "调用成功",
      "value": {
          "popList": [
              {
                  "activity": {
                      "activityId": 516,
                      "activityName": "淘金币右侧异形",
                      "endTime": 1761926399000,
                      "pageId": 263,
                      "priority": 999999,
                      "sceneCode": "home",
                      "startTime": 1738890000000,
                      "status": 1
                  },
                  "callback": {
                      "shouldCallback": true,
                      "shouldTrack": true,
                      "track": {
                          "common": {
                              "activityId": 516,
                              "dispatchId": "f10d08ad2b2340fc907b75d27f46d793",
                              "loginStatus": 1,
                              "pageId": 263,
                              "sceneCode": "home",
                              "templateCode": "slideStaticResPop",
                              "traceId": "213e3f9c17452021472663378e1e6c"
                          },
                          "custom": {
                              "custom_content_source": "pop_516",
                              "env": "pre"
                          }
                      }
                  },
                  "page": {
                      "content": {
                          "bgImageBefore": "https://gw.alicdn.com/imgextra/i3/O1CN01BbaRCe1tJYIZiPH0A_!!6000000005881-2-tps-640-320.png",
                          "displayPosition": "right",
                          "jumpUrl": "https://huodong.taobao.com/wow/z/tbhome/pc-growth/tao-coin"
                      },
                      "pageDesc": "淘金币右pop",
                      "pageId": 263,
                      "pageName": "淘金币右pop",
                      "templateCode": "slideStaticResPop"
                  },
                  "template": {
                      "attributes": {
                          "cdnjs": "https://g.alicdn.com/code/npm/@ali/tbpc-pop-dynamic-common/1.0.0/index.umd.es5.production.js",
                          "distanceToBottom": "15"
                      },
                      "displayStyle": "float",
                      "source": "custom",
                      "templateCode": "slideStaticResPop",
                      "templateDesc": "可左可右，下间距由技术控制",
                      "templateName": "下方两侧静态资源位"
                  },
                  "track": {
                      "activityId": 516,
                      "activityName": "淘金币右侧异形",
                      "dispatchId": "f10d08ad2b2340fc907b75d27f46d793",
                      "loginStatus": 1,
                      "pageId": 263,
                      "sceneCode": "home",
                      "templateCode": "slideStaticResPop",
                      "traceId": "213e3f9c17452021472663378e1e6c"
                  }
              },
              {
                  "activity": {
                      "activityId": 667,
                      "activityName": "惊喜红包测试活动",
                      "endTime": 1745245002000,
                      "pageId": 382,
                      "priority": 99999999,
                      "sceneCode": "home",
                      "startTime": 1744874194000,
                      "status": 1
                  },
                  "callback": {
                      "shouldCallback": true,
                      "shouldTrack": true,
                      "track": {
                          "common": {
                              "activityId": 667,
                              "dispatchId": "bb3058727ed54e028da240f790b5aabe",
                              "loginStatus": 1,
                              "pageId": 382,
                              "sceneCode": "home",
                              "templateCode": "surpriseConsumerCoupon",
                              "traceId": "213e3f9c17452021472663378e1e6c"
                          },
                          "custom": {
                              "custom_content_source": "pop_667",
                              "env": "pre"
                          }
                      }
                  },
                  "page": {
                      "content": {
                          "hairTitle": "消费券",
                          "channel": "lafite_PChongbao",
                          "asac": "2A24514KMM6TWY02RE4CPQ",
                          "action": "去使用",
                          "bgImage": "https://img.alicdn.com/imgextra/i3/O1CN01tnKRT01yZeJLP2TE2_!!6000000006593-2-tps-640-764.png",
                          "jumpUrl": "https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-561441",
                          "desc": "可叠加官方立减"
                      },
                      "pageId": 382,
                      "pageName": "惊喜红包测试",
                      "templateCode": "surpriseConsumerCoupon"
                  },
                  "template": {
                      "attributes": {
                          "cdnjs": "https://dev.g.alicdn.com/code/npm/@ali/tbpc-pop-dynamic-common/1.0.7/index.umd.es5.development.js"
                      },
                      "displayStyle": "window",
                      "source": "custom",
                      "templateCode": "surpriseConsumerCoupon",
                      "templateDesc": "两个红包玩法，2025年4月大促开发",
                      "templateName": "惊喜红包（两个红包玩法）"
                  },
                  "track": {
                      "activityId": 667,
                      "activityName": "惊喜红包测试活动",
                      "dispatchId": "bb3058727ed54e028da240f790b5aabe",
                      "loginStatus": 1,
                      "pageId": 382,
                      "sceneCode": "home",
                      "templateCode": "surpriseConsumerCoupon",
                      "traceId": "213e3f9c17452021472663378e1e6c"
                  }
              },
              {
                "activity": {
                  "activityId": 723,
                  "activityName": "我的权益催用",
                  "endTime": 1748274900000,
                  "pageId": 307,
                  "priority": 99999,
                  "sceneCode": "home",
                  "startTime": 1747398600000,
                  "status": 1
                },
                "callback": {
                  "shouldCallback": true,
                  "shouldTrack": true,
                  "track": {
                    "common": {
                      "activityId": 723,
                      "dispatchId": "fcf58ac06b654b5281464c42f119ae38",
                      "loginStatus": 1,
                      "pageId": 307,
                      "sceneCode": "home",
                      "templateCode": "myBenefitRemindPop",
                      "traceId": "213e3f9c17480504151894055e16b5"
                    },
                    "custom": {
                      "custom_content_source": "pop_723",
                      "env": "pre"
                    }
                  }
                },
                "page": {
                  "content": {
                    "bizId": "6",
                    "jumpUrl": "https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-546975",
                    "actionAfter": "查看可用商品"
                  },
                  "pageId": 307,
                  "pageName": "我的权益催用",
                  "templateCode": "myBenefitRemindPop"
                },
                "template": {
                  "attributes": {
                    "cdnjs": "https://g.alicdn.com/code/npm/@ali/tbpc-pop-dynamic-common/1.0.13/index.umd.es5.production.js"
                  },
                  "displayStyle": "window",
                  "source": "custom",
                  "templateCode": "myBenefitRemindPop",
                  "templateDesc": "我的红包和卡券包催用",
                  "templateName": "我的权益催用pop"
                },
                "track": {
                  "activityId": 723,
                  "activityName": "我的权益催用",
                  "dispatchId": "fcf58ac06b654b5281464c42f119ae38",
                  "loginStatus": 1,
                  "pageId": 307,
                  "sceneCode": "home",
                  "templateCode": "myBenefitRemindPop",
                  "traceId": "213e3f9c17480504151894055e16b5"
                }
              },
              {
                "activity": {
                  "activityId": 667,
                  "activityName": "惊喜红包测试活动",
                  "endTime": 1745245002000,
                  "pageId": 382,
                  "priority": 99999999,
                  "sceneCode": "home",
                  "startTime": 1744874194000,
                  "status": 1
                },
                "callback": {
                  "shouldCallback": true,
                  "shouldTrack": true,
                  "track": {
                    "common": {
                      "activityId": 667,
                      "dispatchId": "bb3058727ed54e028da240f790b5aabe",
                      "loginStatus": 1,
                      "pageId": 382,
                      "sceneCode": "home",
                      "templateCode": "surpriseDoubleRedpacket",
                      "traceId": "213e3f9c17452021472663378e1e6c"
                    },
                    "custom": {
                      "custom_content_source": "pop_667",
                      "env": "pre"
                    }
                  }
                },
                "page": {
                  "content": {
                    "hairTitle": "惊喜红包",
                    "channel": "lafite_PChongbao",
                    "asac": "2A24514KMM6TWY02RE4CPQ",
                    "action": "开心收下",
                    "bgImage": "https://img.alicdn.com/imgextra/i3/O1CN01tnKRT01yZeJLP2TE2_!!6000000006593-2-tps-640-764.png",
                    "jumpUrl": "https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-561441",
                    "desc": "可叠加立减折上折"
                  },
                  "pageId": 382,
                  "pageName": "惊喜红包测试",
                  "templateCode": "surpriseDoubleRedpacket"
                },
                "template": {
                  "attributes": {
                    "cdnjs": "https://dev.g.alicdn.com/code/npm/@ali/tbpc-pop-dynamic-common/1.0.7/index.umd.es5.development.js"
                  },
                  "displayStyle": "window",
                  "source": "custom",
                  "templateCode": "surpriseDoubleRedpacket",
                  "templateDesc": "两个红包玩法，2025年4月大促开发",
                  "templateName": "惊喜红包（两个红包玩法）"
                },
                "track": {
                  "activityId": 667,
                  "activityName": "惊喜红包测试活动",
                  "dispatchId": "bb3058727ed54e028da240f790b5aabe",
                  "loginStatus": 1,
                  "pageId": 382,
                  "sceneCode": "home",
                  "templateCode": "surpriseDoubleRedpacket",
                  "traceId": "213e3f9c17452021472663378e1e6c"
                }
              }
          ],
          "popup": true
      }
  },
  "ret": [
      "SUCCESS::调用成功"
  ],
  "traceId": "213e3f9c17452021472663378e1e6c",
  "v": "1.0"
}
